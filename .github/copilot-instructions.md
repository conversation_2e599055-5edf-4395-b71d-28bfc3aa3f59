# Korapay Business Admin Frontend - AI Agent Instructions

## Architecture Overview

This is a **React 18 + TypeScript fintech admin dashboard** built with **Vite** for a payments platform. The app uses a **hybrid state management** approach:
- **Zustand** for authentication and bulk actions (`src/store/`)
- **React Query** for server state management
- **Custom hooks pattern** for business logic (`src/hooks/`)

## Key Development Workflows

### Component Generation
- **Always use `npm run gen`** to scaffold new components/pages
- Templates are in `templates/` folder (`.hbs` files)
- Follow the prompted flow: Component (c) or Page (p), name, container path

### Development Commands
```bash
npm run start:dev     # Vite dev server on port 4200
npm run build         # Production build
npm run test          # Vitest with coverage
npm run lint:all      # ESLint with auto-fix
npm run type-check    # TypeScript validation
```

## Critical Path Imports (Use These Aliases)

```typescript
import { APIRequest } from '+services/api-services';
import { usePermission, usePageHeader } from '+hooks';
import ComponentName from '+dashboard/ModuleName';
import { history, sessionKeys } from '+utils';
import useStore from '+store';
```

## Container Structure Pattern

The app follows a **strict 3-layer container hierarchy**:

1. **`src/containers/Auth/`** - Authentication flows
2. **`src/containers/Dashboard/`** - Main business modules (Merchants, PayIn, PayOut, etc.)
3. **`src/containers/Dashboard/Shared/`** - Reusable dashboard components

### Dashboard Module Pattern
Each dashboard module follows this structure:
```
Dashboard/ModuleName/
├── index.tsx              # Main route component
├── components/            # Non-routable sub-components
├── SubRoute/             # Nested route pages
│   ├── index.tsx
│   └── components/
└── ModuleName.spec.tsx   # Tests
```

## Permission System Integration

**Every dashboard component must use `usePermission` hook:**
```typescript
const { userHasPermission } = usePermission();
// Check against permissions defined in dashboardRoutes.ts
```

Routes are mapped in `src/containers/Dashboard/Shared/data/dashboardRoutes.ts` with permission keys.

## API Service Pattern

**Use the centralized API service** - don't create axios instances:
```typescript
import APIRequest from '+services/api-services';

// Use existing methods in api-services.ts (3000+ lines)
// Check existing endpoints before creating new ones
const response = await APIRequest.someExistingMethod(payload);
```

## State Management Rules

1. **Authentication state**: Use `useStore()` (Zustand)
2. **Server data**: Use React Query hooks
3. **Component state**: Use `useReducerState` from hooks (custom pattern)
4. **Bulk actions**: Use `useBulkActionStore()` (Zustand slice)

## Styling Approach

- **SCSS modules** with Bootstrap 4 base
- Main styles: `src/styles/main.scss`
- Custom Korapay styles: `src/styles/kpy-custom/`
- Component-specific: `ComponentName.scss` alongside component

## Error Handling & Logging

**Never use `console.log`** - use Logger utility:
```typescript
import { Logger } from '+utils';
Logger.error('Error message', errorObject);
```

## Environment Variables

Use `REACT_APP_` prefix for Vite compatibility:
- `REACT_APP_MIDDLEWARE_API_BASE` - Main API endpoint
- `REACT_APP_REDIRECT_URI` - OAuth redirect
- See `.env.sample` for full list with mythological naming convention

## Testing Patterns

- **Vitest** for unit tests
- **React Testing Library** for component tests
- **MSW** for API mocking
- Coverage targets: 26% branches, 21% functions, 31% lines/statements

## Key Integration Points

1. **Pusher** for real-time notifications (`src/services/pusher.ts`)
2. **Bugsnag** for error tracking
3. **Datadog RUM** for monitoring
4. **React Query** with 10-minute stale time and custom retry logic
