export interface IUpdateTransactionForm {
  currentStatus: 'processing';
  selectedRows: string[];
  onCloseModal: () => void;
  clearSelection: () => void;
}

export type FormModeType = 'newStatus' | 'reason' | 'checkbox';

export const formStage = {
  stage_1: 'stage_1',
  stage_2: 'stage_2',
  stage_3: 'stage_3'
} as const;

export type FormStageType = (typeof formStage)[keyof typeof formStage];

type transactionStatusActionType = 'payin_approve' | 'payin_decline' | 'payout_approve' | 'payout_decline';

export type transactionStatusPayloadType = {
  type: transactionStatusActionType;
  references: string[];
  reason: string;
  currency: string | undefined;
};

