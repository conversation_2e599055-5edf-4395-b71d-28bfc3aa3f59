export type KeyPersonnelType = {
  name: string;
  role: string;
  address: string;
  occupation?: string | null;
  share_type?: string;
  share_count?: number;
  share_value?: number;
  share_percentage?: number;
  identification_type?: string | null;
  identification_number?: string | null;
};

export type MerchantRegistrationType = {
  id: number;
  merchant_id: number;
  key_personnel: KeyPersonnelType[];
  created_at: string;
  updated_at: string;
  merchant: {
    id: number;
    name: string;
    email: string;
    country_id: number;
    created_at: string;
    tier_level: string;
    status: string;
    number_of_representatives: number;
    number_of_directors: number;
    number_of_shareholders: number;
    location: string;
    industry: string;
    iso2: string;
  };
};
