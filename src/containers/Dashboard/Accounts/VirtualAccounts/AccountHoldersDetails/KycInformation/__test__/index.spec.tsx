import React from 'react';
import { act, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import { mockedAccountHolderKycDetail, mockedVbaHolder } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';

import { TData } from '../../TypesAccountHolders';
import KycInformation from '../index';

const kycData = { ...mockedAccountHolderKycDetail.data, ...mockedVbaHolder.data } as unknown as TData;

const MockedKycInformation = ({ data }: { data: TData }) => {
  return (
    <MockIndex>
      <KycInformation data={data as unknown as TData} />
    </MockIndex>
  );
};

describe('KycInformation', () => {
  test('KycInformation is accessible', async () => {
    const { container } = render(<MockedKycInformation data={kycData} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('Does not render loading screen when data has been fetched', () => {
    render(<MockedKycInformation data={kycData} />);
    expect(screen.queryByTestId('loading-placeholder')).not.toBeInTheDocument();
  });

  test('Renders KycInformation Nav and list', () => {
    render(<MockedKycInformation data={kycData} />);
    expect(screen.getByTestId('kyc-nav')).toBeInTheDocument();
    expect(screen.getAllByTestId('kyc-nav-item')).toHaveLength(3);
  });

  test('Renders the first nav-item component by default ', () => {
    render(<MockedKycInformation data={kycData} />);
    expect(screen.getByText(/Account Holder's KYC Information/i)).toBeInTheDocument();
  });

  test('Clicking the Kyc 2nd nav item should render the kyc holder documents', async () => {
    render(<MockedKycInformation data={kycData} />);
    const nav = screen.getAllByTestId('kyc-nav-item');

    act(() => nav[1].click());
    await screen.findByTestId('kyc-nav');

    await screen.findByText(/proof of address/i);
    await screen.findByText(/id document/i);
    await screen.findByText(/account statement/i);
    await screen.findByText(/selfie/i);
  });

  test('View kyc documents for an account holder', async () => {
    render(<MockedKycInformation data={kycData} />);

    const nav = screen.getAllByTestId('kyc-nav-item');
    await userEvent.click(nav[1]);

    const viewDocBtns = await screen.findAllByText(/view/i);
    expect(viewDocBtns).toHaveLength(4);

    await userEvent.click(viewDocBtns[0]);

    const closeViewModal = await screen.findByTestId('close-button');
    await userEvent.click(closeViewModal);

    expect(screen.queryByTestId('close-button')).not.toBeInTheDocument();
  });

  test('Clicking the 3rd nav item should fetch and render the kyc history appropriate component', async () => {
    render(<MockedKycInformation data={kycData} />);
    const nav = screen.getAllByTestId('kyc-nav-item');
    expect(nav).toHaveLength(3);

    act(() => nav[2].click());
    expect(screen.getByTestId('kyc-rejections')).toBeInTheDocument();

    const kycRejectionRows = await screen.findAllByTestId('kyc-rejection-row');
    await waitFor(() => expect(kycRejectionRows).toHaveLength(2));
    await waitFor(() => expect(kycRejectionRows[0]).toHaveTextContent(/kyc approved/i));
    await waitFor(() => expect(kycRejectionRows[1]).toHaveTextContent(/kyc rejected/i));
  });

  test('render account kyc details in the kyc details subnav section ', () => {
    render(<MockedKycInformation data={kycData} />);

    expect(screen.getByText(/occupation/i)).toBeInTheDocument();
    expect(screen.getByText(/phone number/i)).toBeInTheDocument();
    expect(screen.getByText(/id document no./i)).toBeInTheDocument();
    expect(screen.getByText(/id document type/i)).toBeInTheDocument();
    expect(screen.getByText(/date of birth/i)).toBeInTheDocument();
    expect(screen.getByText(/address line 1/i)).toBeInTheDocument();
    expect(screen.getByText(/city/i)).toBeInTheDocument();
    expect(screen.getAllByText(/country/i)).toHaveLength(2);
    expect(screen.getByText(/country code/i)).toBeInTheDocument();
  });

  test('render "not available" for a kyc detail is not provided in the response data', () => {
    const data = { ...kycData, account_summary: { ...kycData.account_summary, phone: null } } as unknown as TData;
    render(<MockedKycInformation data={data} />);
    expect(screen.getByText(/not provided/i)).toBeInTheDocument();
  });

  test('verify holder kyc flow', async () => {
    const data = { ...kycData };
    data.account_summary.account_status = 'pending';
    render(<MockedKycInformation data={data} />);
    expect(screen.getByText(/needs to be verified/i)).toBeInTheDocument();

    await userEvent.click(screen.getByTestId('approve'));
    expect(screen.getByText(/yes, verify/i)).toBeInTheDocument();

    await userEvent.click(screen.getByText(/yes, verify/i));
    await screen.findByText(/dismiss/i);
  });

  test('decline holder kyc flow', async () => {
    const data = { ...kycData };
    render(<MockedKycInformation data={data} />);
    expect(screen.getByText(/needs to be verified/i)).toBeInTheDocument();

    await userEvent.click(screen.getByTestId('decline'));
    expect(screen.getByText(/yes, decline/i)).toBeInTheDocument();
    expect(screen.getByText(/yes, decline/i)).toBeDisabled();

    await userEvent.type(screen.getByTestId('decline-kyc-input'), 'Document not valid');
    expect(screen.getByText(/yes, decline/i)).toBeEnabled();

    await userEvent.click(screen.getByText(/yes, decline/i));
    await screen.findByText(/dismiss/i);
  });
});
