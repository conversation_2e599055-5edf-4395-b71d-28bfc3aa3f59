.kyc-info {
  margin-top: 2rem;
  font-size: 1rem;

  .kyc-action {
    align-items: flex-start;
    background-color: #fff8e1;
    border-radius: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    width: 100%;
    padding: 1rem;
    margin: 0 0 1.5rem;

    img {
      width: 24px;
      height: 24px;
    }

    .action-info {
      flex-grow: 1;
      align-self: center;

      h6 {
        margin: 0;
      }

      p {
        max-width: 450px;
        margin: 0.25rem 0 0;
      }
    }

    .btn-group {
      display: flex;
      gap: 8px;
      margin-left: auto;

      button {
        all: unset;
        border-radius: 5px;
        background-color: #f32345;
        color: #fff;
        cursor: pointer;
        font-size: 0.85rem;
        padding: 0.3rem 1rem;
      }

      button.approve {
        background-color: #2376f3;
      }
    }
  }

  &__title {
    font-weight: 600;
    font-size: 1.1rem;
    letter-spacing: -0.02em;
    color: #4e555b;
    margin: 1rem 0;
  }

  &__description {
    letter-spacing: -0.02em;
    max-width: 450px;
  }

  .vertical-menu {
    padding-inline-start: 0;
    li {
      color: #414f5f;
      font-size: 14px;
      list-style-type: none;

      button {
        all: unset;
        cursor: pointer;
        display: block;
        max-width: 300px;
        padding: 12px 15px;
      }
    }

    li:hover {
      color: #2376f3;
      font-weight: 500;
    }

    .active {
      color: #2376f3;
      background-color: #f1f6fa;
      border-radius: 8px;
      font-weight: 500;
    }
  }

  .nav-content-wrapper {
    margin: 2.5rem 0;
  }

  .grey-text {
    color: #94a7b7;
  }

  &__rejected {
    background-color: #eaf2fe;
    border-radius: 8px;
    display: flex;
    flex-wrap: wrap;
    font-size: 1rem;
    margin-top: 1rem;
    padding: 0.75rem 1rem;

    .title-wrapper {
      display: flex;
      align-items: center;
      gap: 0.25rem;

      img {
        width: 16px;
        height: 16px;
      }
    }

    p {
      max-width: 450px;
      margin-top: 0.5rem;
    }

    p::first-letter {
      text-transform: uppercase;
    }

    .timestamp {
      margin-left: auto;
    }
  }

  .no-rejections {
    font-size: 1.1rem;
    font-weight: 300;
  }

  .kyc-modal {
    .__title {
      & > :first-child {
        font-weight: 600;
      }

      span:nth-of-type(2) {
        color: #94a7b7;
      }

      span:nth-of-type(2)::before {
        content: '';
        margin: 0 0.7rem;
        border-left: 2px solid #dde2ec;
      }
    }
    .__info {
      background: #fff7ed;
      border-radius: 0.5rem;
      color: #a9afbc;
      font-weight: 500;
      margin-top: 1.5rem;
      padding: 1.2rem;
    }

    &__success {
      text-align: center;

      h6 {
        margin-top: 1rem;
      }

      p {
        font-weight: 300;
      }

      button {
        all: unset;
        color: #2376f3;
        cursor: pointer;
        font-weight: 600;
        margin: 1.5rem 0;
      }
    }
  }

  .kyc-doc-group {
    .doc {
      align-items: center;
      border-bottom: 1px solid #dde2ec;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      gap: 1rem;
      padding: 1rem 0;

      .doc-type,
      .view-doc {
        display: flex;
        align-items: center;
      }

      .view-doc {
        gap: 0.3rem;

        button {
          margin: 0;
          padding: 0;
        }

        .image-viewer {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
  }
}
