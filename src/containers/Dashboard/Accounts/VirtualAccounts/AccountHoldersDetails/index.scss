.account-holder {
  .content-details-title {
    .title-wrapper {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .title-icon {
        height: 24px;
        max-width: 24px;
      }

      span {
        font-weight: 500;
        font-size: 1rem;
        color: #a9afbc;
      }
      h3 {
        text-transform: capitalize;
        width: 100%;
      }
    }
  }
}

.acc-summary {
  margin-top: 1rem;

  td {
    width: 250px;
    font-size: 1rem;
  }

  span {
    font-size: 1rem;
  }

  .title {
    color: #a9afbc;
  }
}

.reason-modal {
  .title,
  .description {
    background-color: #f9fbfd;
    border: 2px solid #dde2ec;
    border-radius: 5px;
    margin: 0 0 2rem;
    padding: 1rem 0.5rem;
  }

  .title::first-letter,
  .description::first-letter {
    text-transform: uppercase;
  }

  p {
    font-size: 1rem;
    font-weight: 500;
  }
}
