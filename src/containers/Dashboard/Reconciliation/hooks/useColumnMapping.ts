import { useState } from 'react';
import { useHistory } from 'react-router-dom';
import { useShallow } from 'zustand/react/shallow';

import useReconciliationStore from '+store/reconciliationStore';
import { IProcessorReportMapping, MappingType, ProcessorConfigDataType } from '+types';

import { generateColor } from '../helpers/reconcileReportHelper';

const generateStableId = (mapping: IProcessorReportMapping, type: string, index: number) => {
  const baseContent = `${type}-${mapping.processor_report || 'empty'}-${mapping.internal_report || 'empty'}`;
  const stableContent = btoa(baseContent).replace(/[^a-zA-Z0-9]/g, '');
  return `${stableContent}-${index}`;
};

const validateBulkDeleteIds = (bulkIds: string[], currentMappings: { id?: string }[]): string[] => {
  const validIds = new Set(currentMappings.map(mapping => mapping.id).filter(Boolean));
  return bulkIds.filter(id => validIds.has(id));
};

const useColumnMapping = () => {
  const history = useHistory();
  const [removingItems, setRemovingItems] = useState<Set<string>>(new Set());
  const setKeyMappings = useReconciliationStore(state => state.setKeyMappings);

  const deleteKeyMapping = useReconciliationStore(state => state.deleteKeyMapping);
  const updateKeyMappings = useReconciliationStore(state => state.updateKeyMappings);
  const clearKeyMappings = useReconciliationStore(state => state.clearKeyMappings);
  const setStartReconciliationData = useReconciliationStore(state => state.setStartReconciliationData);
  const setBulkDeleteIds = useReconciliationStore(state => state.setBulkDeleteIds);

  const {
    startReconciliationData,
    primaryKeyMappings,
    comparisonKeyMappings,
    referenceKeyMappings,
    statusKeyMappings,
    bulkDeleteIds,
    autoMatchColumns
  } = useReconciliationStore(
    useShallow(state => ({
      startReconciliationData: state.startReconciliationData,
      primaryKeyMappings: state.primaryKeyMappings,
      comparisonKeyMappings: state.comparisonKeyMappings,
      referenceKeyMappings: state.referenceKeyMappings,
      statusKeyMappings: state.statusKeyMappings,
      autoMatchColumns: state.autoMatchColumns,
      bulkDeleteIds: state.bulkDeleteIds
    }))
  );

  const setDefaultData = () => {
    const defaultPrimary = { processor_report: '', internal_report: '', color: generateColor() };
    const defaultComparison = { processor_report: '', internal_report: '' };
    const defaultReference = { processor_report: '', internal_report: '' };
    const defaultStatus = { processor_report: '', internal_report: '', color: generateColor() };

    setKeyMappings([{ ...defaultPrimary, id: generateStableId(defaultPrimary, 'primary', 0) }], 'primaryKeyMappings');
    setKeyMappings([{ ...defaultComparison, id: generateStableId(defaultComparison, 'comparison', 0) }], 'comparisonKeyMappings');
    setKeyMappings([{ ...defaultReference, id: generateStableId(defaultReference, 'reference', 0) }], 'referenceKeyMappings');
    setKeyMappings([{ ...defaultStatus, id: generateStableId(defaultStatus, 'status', 0) }], 'statusKeyMappings');
  };
  const handleMappings = (data: { data: ProcessorConfigDataType[] }) => {
    clearKeyMappings('comparisonKeyMappings', false);
    clearKeyMappings('referenceKeyMappings', false);
    clearKeyMappings('primaryKeyMappings', false);
    clearKeyMappings('statusKeyMappings', false);

    const actualData = data?.data;
    if (actualData.length > 0) {
      const modifiedPrimaryKeyMappings = actualData[0].primary_key_mappings.map((mapping: IProcessorReportMapping, index: number) => ({
        ...mapping,
        color: generateColor(index),
        id: generateStableId(mapping, 'primary', index)
      }));
      setKeyMappings(modifiedPrimaryKeyMappings, 'primaryKeyMappings');
      setKeyMappings(
        actualData[0].comparison_key_mappings.map((mapping: IProcessorReportMapping, index: number) => ({
          ...mapping,
          id: generateStableId(mapping, 'comparison', index)
        })),
        'comparisonKeyMappings'
      );

      const modifiedReferenceKeyMappings = [
        {
          ...actualData[0].reference_key_mapping,
          color: generateColor(),
          id: generateStableId(actualData[0].reference_key_mapping, 'reference', 0)
        }
      ];

      setKeyMappings(modifiedReferenceKeyMappings, 'referenceKeyMappings');

      if (actualData[0].metadata?.statusMap && Object.keys(actualData[0].metadata.statusMap).length > 0) {
        const statusMap = actualData[0].metadata.statusMap;
        const modifiedStatusKeyMappings = Object.entries(statusMap).map(([internal_report, processor_report], index) => ({
          processor_report,
          internal_report,
          color: generateColor(index),
          id: generateStableId({ processor_report, internal_report }, 'status', index)
        }));
        setKeyMappings(modifiedStatusKeyMappings, 'statusKeyMappings');
      }

      setTimeout(() => {
        const validationData = [
          {
            type: 'primaryKeyMappings' as MappingType,
            currentIds: bulkDeleteIds.primaryKeyMappings,
            mappings: modifiedPrimaryKeyMappings
          },
          {
            type: 'comparisonKeyMappings' as MappingType,
            currentIds: bulkDeleteIds.comparisonKeyMappings,
            mappings: actualData[0].comparison_key_mappings.map((mapping, index) => ({
              id: generateStableId(mapping, 'comparison', index)
            }))
          },
          {
            type: 'referenceKeyMappings' as MappingType,
            currentIds: bulkDeleteIds.referenceKeyMappings,
            mappings: modifiedReferenceKeyMappings
          }
        ];

        if (actualData[0].metadata?.statusMap && Object.keys(actualData[0].metadata.statusMap).length > 0) {
          const statusMap = actualData[0].metadata.statusMap;
          const statusMappings = Object.entries(statusMap).map(([internal_report, processor_report], index) => ({
            id: generateStableId({ processor_report, internal_report }, 'status', index)
          }));

          validationData.push({
            type: 'statusKeyMappings' as MappingType,
            currentIds: bulkDeleteIds.statusKeyMappings,
            mappings: statusMappings
          });
        }

        validationData.forEach(({ type, currentIds, mappings }) => {
          const validIds = validateBulkDeleteIds(currentIds, mappings);
          if (validIds.length !== currentIds.length) {
            setBulkDeleteIds(validIds, type);
          }
        });
      }, 100);
    } else {
      setDefaultData();
    }
  };

  const handleAddNewColumn = (mappingType: MappingType) => {
    const newMapping = { processor_report: '', internal_report: '', color: generateColor() };

    let currentMappings;
    if (mappingType === 'primaryKeyMappings') {
      currentMappings = primaryKeyMappings;
    } else if (mappingType === 'comparisonKeyMappings') {
      currentMappings = comparisonKeyMappings;
    } else if (mappingType === 'referenceKeyMappings') {
      currentMappings = referenceKeyMappings;
    } else {
      currentMappings = statusKeyMappings;
    }
    const newIndex = currentMappings.length;
    setKeyMappings([{ ...newMapping, id: generateStableId(newMapping, mappingType.replace('KeyMappings', ''), newIndex) }], mappingType);
  };

  const handleOptionChange = (
    value: string | number | (string | number)[],
    field: keyof IProcessorReportMapping,
    id: string,
    type: MappingType
  ) => {

    updateKeyMappings({ value: { [field]: value }, id }, type);

    if (!autoMatchColumns) return;

    const valueStr = String(value);

    let currentMappings = primaryKeyMappings;
    if (type === 'comparisonKeyMappings') currentMappings = comparisonKeyMappings;
    else if (type === 'referenceKeyMappings') currentMappings = referenceKeyMappings;

    const current = currentMappings.find(m => m.id === id);
    if (!current) return;

    const isProcessorChange = field === 'processor_report';
    const isInternalChange = field === 'internal_report';

    let counterpartUpdate: Partial<IProcessorReportMapping> | null = null;

    if (isProcessorChange) {
      const match = primaryKeyMappings.find(p => p.processor_report === valueStr);
      if (match?.internal_report && current.internal_report !== match.internal_report) {
        counterpartUpdate = { internal_report: match.internal_report };
      }
    } else if (isInternalChange) {
      const match = primaryKeyMappings.find(p => p.internal_report === valueStr);
      if (match?.processor_report && current.processor_report !== match.processor_report) {
        counterpartUpdate = { processor_report: match.processor_report };
      }
    }

    if (counterpartUpdate) {
      updateKeyMappings({ value: counterpartUpdate, id }, type);
    }
  };

  const handleDelete = (id: string, mappingType: MappingType) => {
    setRemovingItems(prev => new Set(prev).add(id));

    setTimeout(() => {
      deleteKeyMapping(id, mappingType);

      setRemovingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }, 300);
  };
  const handleBulkDelete = (ids: string[], mappingType: MappingType) => {
    setBulkDeleteIds(ids, mappingType);
  };

  const handleCancel = () => {
    history.goBack();
    clearKeyMappings('all');
  };

  return {
    handleMappings,
    setKeyMappings,
    handleAddNewColumn,
    handleOptionChange,
    handleDelete,
    removingItems,
    updateKeyMappings,
    primaryKeyMappings,
    comparisonKeyMappings,
    referenceKeyMappings,
    statusKeyMappings,
    clearKeyMappings,
    handleCancel,
    setStartReconciliationData,
    startReconciliationData,
    setDefaultData,
    handleBulkDelete,
    bulkDeleteIds
  };
};

export default useColumnMapping;
