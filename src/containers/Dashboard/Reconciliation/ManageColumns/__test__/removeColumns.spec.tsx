import React from 'react';
import { act, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { http, HttpResponse } from 'msw';
import { beforeEach, describe, expect, it } from 'vitest';

import { mockProcessorConfigResponseForRemoval, mockRemoveColumnsStoreState } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';
import useReconciliationStore from '+store/reconciliationStore';

import RemoveColumns from '../RemoveColumns';

const MockedRemoveColumns = () => (
  <MockIndex>
    <RemoveColumns />
  </MockIndex>
);

describe('RemoveColumns', () => {
  beforeEach(() => {
    useReconciliationStore.setState({
      startReconciliationData: mockRemoveColumnsStoreState,
      primaryKeyMappings: [],
      comparisonKeyMappings: [],
      referenceKeyMappings: [],
      statusKeyMappings: [],
      bulkDeleteIds: { primaryKeyMappings: [], comparisonKeyMappings: [], referenceKeyMappings: [], statusKeyMappings: [] }
    } as any);

    server.use(
      http.get('/admin/settlement-reconciliations/processor-configs', ({ request }) => {
        const url = new URL(request.url);
        const processor = url.searchParams.get('processor');
        const paymentType = url.searchParams.get('payment_type');
        if (processor === 'korapay' && paymentType === 'payin') {
          return HttpResponse.json(mockProcessorConfigResponseForRemoval, { status: 200 });
        }
        return HttpResponse.json({ status: true, data: [] }, { status: 200 });
      })
    );
  });

  it('loads config and renders sections including Status Mapping', async () => {
    render(<MockedRemoveColumns />);

    await waitFor(() => {
      expect(screen.getByText('Remove Reconciliation Columns')).toBeInTheDocument();
    });

    expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
    expect(screen.getByText('Reference Mapping')).toBeInTheDocument();
    expect(screen.getByText('Comparison Mapping')).toBeInTheDocument();
    expect(screen.getByText('Status Mapping (optional)')).toBeInTheDocument();
  });

  it('cascades bulk selections from Primary to related sections and persists across refresh', async () => {
    const user = userEvent.setup();
    render(<MockedRemoveColumns />);

    await waitFor(() => {
      expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
    });

    await waitFor(() => {
      const state = useReconciliationStore.getState();
      expect(state.primaryKeyMappings.length).toBeGreaterThan(0);
    });

    const checkboxes = screen.getAllByRole('checkbox').filter(cb => cb.getAttribute('data-testid') !== 'consent-checkbox');
    expect(checkboxes.length).toBeGreaterThan(0);

    await user.click(checkboxes[0]);
    if (checkboxes.length > 1) {
      await user.click(checkboxes[1]);
    }

    const state1 = useReconciliationStore.getState();
    expect(state1.bulkDeleteIds.primaryKeyMappings.length).toBeGreaterThan(0);

    expect(state1.bulkDeleteIds.comparisonKeyMappings.length).toBeGreaterThanOrEqual(0);
    expect(state1.bulkDeleteIds.referenceKeyMappings.length).toBeGreaterThanOrEqual(0);
    expect(state1.bulkDeleteIds.statusKeyMappings.length).toBeGreaterThanOrEqual(0);

    render(<MockedRemoveColumns />);

    const state2 = useReconciliationStore.getState();
    expect(state2.bulkDeleteIds.primaryKeyMappings.length).toBe(state1.bulkDeleteIds.primaryKeyMappings.length);
  });

  it('disables Remove Selected Columns button when removal would leave an empty section', async () => {
    render(<MockedRemoveColumns />);

    await waitFor(() => {
      expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
    });

    await waitFor(() => {
      const state = useReconciliationStore.getState();
      expect(state.primaryKeyMappings.length).toBeGreaterThan(0);
    });

    const removeButton = screen.getByRole('button', { name: 'Remove Selected Columns' });
    expect(removeButton).toBeDisabled();
  });
  it('requires consent before enabling the Yes, Remove button in the confirmation modal', async () => {
    const user = userEvent.setup();
    render(<MockedRemoveColumns />);

    await waitFor(() => {
      expect(screen.getByText('Remove Reconciliation Columns')).toBeInTheDocument();
    });

    await waitFor(() => {
      const state = useReconciliationStore.getState();
      expect(state.primaryKeyMappings.length).toBeGreaterThan(0);
    });

    const openModalBtn = screen.getByRole('button', { name: 'Remove Selected Columns' });
    expect(openModalBtn).toBeDisabled();

    const st = useReconciliationStore.getState();
    const primaryId = st.primaryKeyMappings[0]?.id as string;

    act(() => {
      useReconciliationStore.setState({
        bulkDeleteIds: {
          ...st.bulkDeleteIds,
          primaryKeyMappings: [primaryId]
        }
      });
    });

    render(<MockedRemoveColumns />);

    await waitFor(() => {
      const enabledBtns = screen.getAllByRole('button', { name: 'Remove Selected Columns' });
      expect(enabledBtns[0]).not.toBeDisabled();
    });

    const enabledBtns = screen.getAllByRole('button', { name: 'Remove Selected Columns' });
    await user.click(enabledBtns[0]);

    const consent = await screen.findByTestId('consent-checkbox');
    const confirmBtn = screen.getByTestId('second-button');
    expect(confirmBtn).toBeDisabled();

    await user.click(consent);

    await waitFor(() => {
      const updatedConfirmBtn = screen.getByTestId('second-button');
      expect(updatedConfirmBtn).not.toBeDisabled();
    });
  });

  it('sends expected payload including metadata.status and metadata.statusMap when confirming removal', async () => {
    const user = userEvent.setup();

    let capturedBody: any = null;
    server.use(
      http.post('/admin/settlement-reconciliations/processor-configs', async ({ request }) => {
        capturedBody = await request.json();
        return HttpResponse.json({ status: true }, { status: 200 });
      })
    );

    render(<MockedRemoveColumns />);

    await waitFor(() => {
      expect(screen.getByText('Primary Mapping')).toBeInTheDocument();
    });

    await waitFor(() => {
      const state = useReconciliationStore.getState();
      expect(state.primaryKeyMappings.length).toBeGreaterThan(0);
    });

    const state = useReconciliationStore.getState();
    const primaryId = state.primaryKeyMappings[0]?.id as string;

    act(() => {
      useReconciliationStore.setState({
        bulkDeleteIds: {
          ...state.bulkDeleteIds,
          primaryKeyMappings: [primaryId]
        }
      });
    });

    render(<MockedRemoveColumns />);

    await waitFor(() => {
      const removeBtns = screen.getAllByRole('button', { name: 'Remove Selected Columns' });
      expect(removeBtns[0]).not.toBeDisabled();
    });

    const removeBtns = screen.getAllByRole('button', { name: 'Remove Selected Columns' });
    await user.click(removeBtns[0]);

    const consent = await screen.findByTestId('consent-checkbox');

    await act(async () => {
      await user.click(consent);
    });

    const yesRemove = screen.getByTestId('second-button');
    await user.click(yesRemove);

    await waitFor(() => {
      expect(capturedBody).toBeTruthy();
    });

    expect(capturedBody).toEqual(
      expect.objectContaining({
        processor: 'korapay',
        payment_type: 'payin',
        primary_key_mappings: expect.any(Array),
        comparison_key_mappings: expect.any(Array),
        reference_key_mapping: expect.objectContaining({ processor_report: 'txn_id', internal_report: 'transaction_id' }),
        metadata: expect.objectContaining({
          status: expect.arrayContaining(['success']),
          statusMap: expect.objectContaining({ success: 'completed' })
        })
      })
    );
  });
});
