import React, { useEffect } from 'react';
import { useMutation, useQuery } from 'react-query';
import { useHistory } from 'react-router-dom';

import LoadingPlaceholder from '+containers/Dashboard/Shared/LoadingPlaceHolder';
import { useFeedbackHandler, useReducerState } from '+hooks';
import APIRequest from '+services/api-services';
import Modal from '+shared/Modal';
import Typography from '+shared/Typography';
import { IManageColumnFormData, ProcessorFormMappingType } from '+types';
import { capitalize, logError } from '+utils';

import ReconcileColumnProcessor from '../components/ReconcileColumnProcessor';
import ReconciliationOptionRowCard from '../components/ReconciliationOptionRowCard';
import { validateColumn } from '../helpers/reconcileReportHelper';
import useColumnMapping from '../hooks/useColumnMapping';

import './index.scss';

const apiRequest = new APIRequest();
const AddColumns = () => {
  const history = useHistory();
  const { feedbackInit } = useFeedbackHandler();

  const {
    handleDelete,
    handleAddNewColumn,
    handleOptionChange,
    removingItems,
    primaryKeyMappings,
    comparisonKeyMappings,
    referenceKeyMappings,
    statusKeyMappings,
    clearKeyMappings,
    handleMappings,
    handleCancel,
    setStartReconciliationData,
    startReconciliationData,
    setDefaultData
  } = useColumnMapping();

  const [state, setState] = useReducerState<ProcessorFormMappingType & { displaySuccessModal: boolean }>({
    processor: startReconciliationData.processor,
    payment_type: startReconciliationData.payment_type,
    displaySuccessModal: false
  });

  const { data, isLoading } = useQuery(
    [`RECONCILIATION_PROCESSOR_CONFIG_${state.payment_type}_${state.processor}`],
    () => apiRequest.getSettlementReconciliationProcessorConfig(state.processor, state.payment_type),
    {
      refetchOnMount: 'always',
      staleTime: 0,
      onSuccess(data) {
        handleMappings(data);
      },
      enabled: !!state.processor && !!state.payment_type
    }
  );

  useEffect(() => {
    setStartReconciliationData({ ...startReconciliationData, processor: state.processor, payment_type: state.payment_type });
    if (primaryKeyMappings.length === 0 && comparisonKeyMappings.length === 0 && referenceKeyMappings.length === 0) {
      setDefaultData();
    }
  }, [data]);

  const handleSetState = (newState: Partial<typeof state>) => {
    setState({ ...state, ...newState });
  };
  const createSettlementConfigMutation = useMutation((value: IManageColumnFormData) => apiRequest.createOrUpdateProcessorConfig(value), {
    onSuccess: () => {
      setState({ ...state, displaySuccessModal: true });
    },
    onError: (error: { response: { data: { data: { amount: { message: string } }; message: string } } }) => {
      logError(error);

      feedbackInit({
        message: capitalize(
          `${error?.response?.data?.data?.amount?.message ?? error?.response?.data?.message}` ||
            'There has been an error creating this settlement config'
        ),
        type: 'danger'
      });
    }
  });

  const handleAddColumns = () => {
    const statusValues = statusKeyMappings.map(item => item.internal_report?.trim()).filter((val): val is string => Boolean(val));

    const data = {
      processor: state.processor,
      payment_type: state.payment_type,
      primary_key_mappings: primaryKeyMappings.map(item => ({
        internal_report: item.internal_report,
        processor_report: item.processor_report
      })),
      comparison_key_mappings: comparisonKeyMappings.map(item => ({
        internal_report: item.internal_report,
        processor_report: item.processor_report
      })),
      reference_key_mapping: referenceKeyMappings.map(item => ({
        internal_report: item.internal_report,
        processor_report: item.processor_report
      }))[0],
      metadata: {
        statusMap: (() => {
          const map = statusKeyMappings?.reduce(
            (acc, item) => {
              if (item.internal_report && item.processor_report) {
                acc[item.internal_report] = item.processor_report;
              }
              return acc;
            },
            {} as Record<string, string>
          );
          return map && Object.keys(map).length ? map : undefined;
        })(),
        // Return undefined when no valid internal_report values exist
        status: statusValues.length ? statusValues : undefined
      }
    } satisfies IManageColumnFormData;

    createSettlementConfigMutation.mutate(data);
  };

  const handleCloseSuccessModal = () => {
    setState({ ...state, displaySuccessModal: false });
    clearKeyMappings('all');
    history.push('/dashboard/reconciliation');
  };

  const handleGoBack = () => {
    clearKeyMappings('all');
    history.goBack();
  };

  return (
    <section className="element-box manage-columns">
      <div className="row manage-columns__back">
        <button type="button" className="btn btn-link" onClick={handleGoBack}>
          <i className="os-icon os-icon-arrow-left7" />
          <span style={{ fontWeight: 500 }}>Back</span>
        </button>
      </div>
      <div className="manage-columns__title">
        <Typography variant="h4" className="mb-20">
          Add Reconciliation Columns
        </Typography>
        <Typography variant="subtitle4">
          Add columns to selected processor and payment type. They will be available for selection during reconciliation
        </Typography>
      </div>
      <section className="manage-columns__content">
        <ReconcileColumnProcessor state={state} setState={handleSetState} />
        {isLoading ? (
          <LoadingPlaceholder type="text" content={2} rows={2} section={2} />
        ) : (
          <>
            <ReconciliationOptionRowCard
              mappings={primaryKeyMappings}
              handleAddNewColumn={() => handleAddNewColumn('primaryKeyMappings')}
              handleDelete={(id: string) => handleDelete(id, 'primaryKeyMappings')}
              handleOptionChange={(value, field, id) => handleOptionChange(value, field, id, 'primaryKeyMappings')}
              label="Primary Mapping"
              isLoading={false}
              removingItems={removingItems}
              message="Primary Mapping connects columns containing the same data, even if their column header names differ between the Internal and Processor reports."
              fieldType="text"
            />
            <ReconciliationOptionRowCard
              mappings={referenceKeyMappings}
              optionKeyMappings={primaryKeyMappings}
              handleOptionChange={(value, field, id) => handleOptionChange(value, field, id, 'referenceKeyMappings')}
              label="Reference Mapping"
              isLoading={false}
              message="Reference Mapping uses a unique identifier common to both reports to match data between Kora and the Processor."
            />
            <ReconciliationOptionRowCard
              mappings={comparisonKeyMappings}
              optionKeyMappings={primaryKeyMappings}
              handleAddNewColumn={() => handleAddNewColumn('comparisonKeyMappings')}
              handleDelete={(id: string) => handleDelete(id, 'comparisonKeyMappings')}
              handleOptionChange={(value, field, id) => handleOptionChange(value, field, id, 'comparisonKeyMappings')}
              label="Comparison Mapping"
              isLoading={false}
              removingItems={removingItems}
              message="Comparison Mapping compares similar columns during the reconciliation process to identify matches or discrepancies."
            />
            <ReconciliationOptionRowCard
              mappings={statusKeyMappings}
              handleAddNewColumn={() => handleAddNewColumn('statusKeyMappings')}
              handleDelete={(id: string) => handleDelete(id, 'statusKeyMappings')}
              handleOptionChange={(value, field, id) => handleOptionChange(value, field, id, 'statusKeyMappings')}
              label="Status Mapping (optional)"
              isLoading={false}
              removingItems={removingItems}
              message="Status Mapping links different status terms that have the same meaning across the Internal and Processor reports."
              fieldType="text"
              controlFieldDisplay
            />{' '}
          </>
        )}
      </section>
      <section className="manage-columns__action">
        <button className="btn text-danger" onClick={handleCancel}>
          Cancel
        </button>
        <button
          className="btn btn-primary"
          onClick={handleAddColumns}
          disabled={
            createSettlementConfigMutation.isLoading ||
            validateColumn(state.processor, primaryKeyMappings, referenceKeyMappings, comparisonKeyMappings, statusKeyMappings)
          }
        >
          {createSettlementConfigMutation.isLoading ? 'Adding...' : 'Add Columns'}
        </button>
      </section>
      <Modal
        visible={state.displaySuccessModal}
        close={handleCloseSuccessModal}
        secondaryCompletedModal
        completedModalSize="md"
        stage="complete"
        completedDescription={`New columns have been added to ${capitalize(state.processor)} processor as Primary Keys, Reference Keys, Comparison Keys and Status Mappings`}
        completedActionText="Dismiss"
      />
    </section>
  );
};

export default AddColumns;
