import React from 'react';
import { Route, Switch } from 'react-router-dom';

import ReconcileReport from './ReconcileReport';
import ReconciliationHistory from './ReconciliationHistory';
import StartReconciliation from './StartReconciliation';
import AddColumns from './ManageColumns/AddColumns';
  import RemoveColumns from './ManageColumns/RemoveColumns';

const Reconciliation = () => {
  return (
    <Switch>
      <Route path="/dashboard/reconciliation" exact component={ReconciliationHistory} />
      <Route path="/dashboard/reconciliation/start" exact component={StartReconciliation} />
      <Route path="/dashboard/reconciliation/reconcile" exact component={ReconcileReport} />
      <Route path="/dashboard/reconciliation/add-columns" exact component={AddColumns} />
      <Route path="/dashboard/reconciliation/remove-columns" exact component={RemoveColumns} />
    </Switch>
  );
};

export default Reconciliation;
