import React from 'react';
import { useQuery } from 'react-query';

import { useFeedbackHandler } from '+hooks';
import APIRequest from '+services/api-services';
import ReactSelectDropdown from '+shared/ReactSelectDropdown';
import { ProcessorFormMappingType, ProcessorType } from '+types';

import { paymentTypeOptions } from '../helpers/reconcileReportHelper';

const apiRequest = new APIRequest();

const ReconcileColumnProcessor = ({
  state,
  setState
}: {
  state: ProcessorFormMappingType;
  setState: (newState: ProcessorFormMappingType) => void;
}) => {
  const { feedbackInit } = useFeedbackHandler();
  const { data: processorOptions, refetch } = useQuery([`PROCESSORLIST`], () => apiRequest.getProcessorList(), {
    keepPreviousData: true,
    refetchOnMount: 'always',
    select: (value: { data: ProcessorType[] }) => {
      const options = value.data.map(item => ({ label: item.name, value: item.slug }));
      return options;
    },
    onError: () => {
      feedbackInit({
        message: `There has been an error fetching processors`,
        type: 'danger',
        action: {
          action: () => {
            refetch();
          },
          name: 'Try again'
        }
      });
    }
  });
  return (
    <section className="manage-columns__content--processor">
      <p>Select Processor & Payment Type</p>
      <div>
        <ReactSelectDropdown
          label="Select Processor"
          options={processorOptions || []}
          onChange={value => setState({ ...state, processor: String(value) })}
          placeholder=""
          value={state.processor}
        />
        <ReactSelectDropdown
          label="Payment Type"
          options={paymentTypeOptions}
          onChange={value => setState({ ...state, payment_type: String(value) as ProcessorFormMappingType['payment_type'] })}
          placeholder=""
          value={state.payment_type}
        />
      </div>
    </section>
  );
};

export default ReconcileColumnProcessor;
