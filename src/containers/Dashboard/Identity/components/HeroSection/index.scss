.ihs {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 20px 0;
  border-radius: 10px;
  margin-bottom: 20px;
  &__title {
    font-size: 1.18rem;
    font-weight: 500;
    margin-bottom: 10px;
    line-height: 22.8px;
  }
  &__description {
    font-size: 0.875rem;
    color: #a9afbc;
    max-width: 70ch;
  }
  &__btn-wrapper {
    display: flex;
    gap: 10px;
    margin-top: 20px;

    &.remove-margin-top {
      margin-top: 0px;
    }

    &.block {
      display: block;
    }
  }
  &__btn {
    all: unset;
    padding: 10px 20px;
    display: flex;
    gap: 10px;
    border-radius: 5px;
    align-items: center;
    font-weight: 600;
    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
    &.success {
      background-color: rgba(36, 179, 20, 1);
      color: rgba(255, 255, 255, 1);

      &:hover {
        background-color: lighten(rgba(36, 179, 20, 1), 5%);
        color: darken(rgba(255, 255, 255, 1), 5%);
      }
    }

    &.danger {
      background-color: rgba(243, 35, 69, 1);
      color: rgba(255, 255, 255, 1);
    }
    &.primary {
      background-color: rgba(35, 118, 243, 1);
      color: rgba(255, 255, 255, 1);
    }
    &.secondary {
      background-color: #eaf2fe;
    }
  }
}
