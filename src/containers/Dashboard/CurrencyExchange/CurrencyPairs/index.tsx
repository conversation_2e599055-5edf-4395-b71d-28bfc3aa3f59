import React, { useRef, useState } from 'react';

import HoverMenu from '+containers/Dashboard/Shared/HoverMenu';
import Icon from '+containers/Dashboard/Shared/Icons';
import { useFeedbackHandler } from '+hooks';
import Table from '+shared/Table';
import { ICurrencyPair, ICurrencyPairTable } from '+types/conversions';
import { capitalizeFirst, getDate, getTime } from '+utils';

import { useFetchCurrencyPairs, useFetchCurrencyRates } from '../components/currencyExchangeHelper';
import CurrencyPairModal from '../components/CurrencyPairModal';

import arrowRight from '+assets/img/dashboard/arrow-right-black.svg';

type CurrencyPairActionType = 'enable' | 'disable' | 'edit' | 'confirm';

const CurrencyPairs = () => {
  const [currencyPairAction, setCurrencyPairAction] = useState<CurrencyPairActionType | ''>('');

  const currentCurrencyPair = useRef<ICurrencyPair>({} as ICurrencyPair);

  const { feedbackInit, closeFeedback } = useFeedbackHandler();

  const { data, isFetching } = useFetchCurrencyPairs(feedbackInit, closeFeedback);

  const { isFetching: isKoraRateFetching, refetch } = useFetchCurrencyRates(
    currentCurrencyPair.current?.from_currency,
    currentCurrencyPair.current?.to_currency,
    !!currentCurrencyPair.current?.from_currency && !!currentCurrencyPair.current?.to_currency && currencyPairAction === 'edit',
    feedbackInit,
    closeFeedback,
    data => {
      currentCurrencyPair.current.kora_rate = data?.data?.current_markup.rate;
      currentCurrencyPair.current.provider_rate = data?.data?.provider_rate;
    }
  );

  const onSelectCurrencyPairAction = (action: CurrencyPairActionType, currencyPair: ICurrencyPair) => {
    if (action !== 'confirm') {
      currentCurrencyPair.current = currencyPair;
    }

    if (action === 'edit') {
      refetch();
    }

    setCurrencyPairAction(action);
  };

  const CurrencyPairTable: ICurrencyPairTable = {
    className: '--currency-pair-table',
    emptyStateHeading: 'No Currency Pairs yet',
    emptyStateMessage: 'It looks like there are no results yet.',
    annotations: 'team member(s)',
    fields: (each: ICurrencyPair) => {
      each.status = each?.enabled ? 'enabled' : 'disabled';
      each.icon = each?.enabled ? 'check' : 'invalid';
      const action = each?.enabled ? 'disable' : 'enable';
      each.icon_color = each?.enabled ? '#24b314' : '#F32345';
      return {
        data: {
          currency_pair: (
            <>
              <span className="currency-pair d-flex align-items-center">
                <p className="m-0">{each.from_currency}</p>
                <img sizes="sm" className="m-0" width={10} height={10} src={arrowRight} alt="arrow right icon" aria-hidden />
                <p className="m-0">{each.to_currency}</p>
              </span>
            </>
          ),
          markup: (
            <span className="markup">
              <strong>{each?.base_markup}</strong>
              <span>%</span>
            </span>
          ),
          pair_status: (
            <div className="d-flex align-items-center p-0">
              <Icon name={each.icon} width={13} height={13} stroke={each.icon_color} />
              <span className={`status ${each.status} ml-2`}>{capitalizeFirst(each.status)}</span>
            </div>
          ),
          last_modified: (
            <div className="d-flex justify-content-between w-100 p-0">
              <span className="last-login">
                <span style={{ display: 'flex', alignItems: 'center' }}>
                  <span>{each?.last_updated ? getDate(each?.last_updated) : '--'}</span>
                  <span className="annotation" style={{ marginLeft: '5px' }}>
                    {each?.last_updated ? getTime(each?.last_updated) : ''}
                  </span>
                </span>
              </span>
              <HoverMenu wrapperClassName="currency-pair-hover-wrapper" className="p-0" title="actions">
                <li>
                  <button
                    type="button"
                    className={`btn btn--link ${action}d`}
                    onClick={() => {
                      onSelectCurrencyPairAction('edit', each);
                    }}
                  >
                    <span className="ml-2">Edit Markup</span>
                  </button>
                </li>
                <li>
                  <button
                    className={`btn btn--link ${action}d`}
                    type="button"
                    onClick={() => {
                      onSelectCurrencyPairAction(action, each);
                    }}
                  >
                    <span className="ml-2">{capitalizeFirst(action)} currency pair</span>
                  </button>
                </li>
              </HoverMenu>
            </div>
          )
        }
      };
    }
  };

  const tableDataKeys = Object.keys(CurrencyPairTable.fields({} as ICurrencyPair).data);

  return (
    <>
      <div className="row">
        <div className="col-sm-12">
          <h6 className="mb-4">Currency Pairs ({data?.data?.length})</h6>
          <Table
            className={CurrencyPairTable.className}
            loading={isFetching}
            data={data?.data || []}
            renderFields
            hasPagination={false}
            tableHeadings={tableDataKeys}
            annotation={CurrencyPairTable.annotations}
            emptyStateHeading={CurrencyPairTable.emptyStateHeading || ''}
            tableWrapperClassName=""
            type="teams"
            emptyStateMessage={CurrencyPairTable.emptyStateMessage || ''}
            totalItems={(data?.data || []).length || 0}
            filterHasAdvancedFilter={false}
            hasFilter={false}
          >
            {CurrencyPairTable.fields}
          </Table>
        </div>
      </div>

      {currencyPairAction !== '' && (
        <CurrencyPairModal
          action={currencyPairAction}
          close={() => setCurrencyPairAction('')}
          currencyPair={currentCurrencyPair.current}
          isMarkupLoading={isKoraRateFetching}
        />
      )}
    </>
  );
};
export default CurrencyPairs;
