import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';
import { vi } from 'vitest';

import { TIconNames } from '+containers/Dashboard/Shared/Icons/IconNames';
import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';

import CurrencyPairModal from '../components/CurrencyPairModal/index';

const defaultCurrencyPair = {
  from_currency: 'USD',
  enabled: true,
  to_currency: 'NGN',
  base_markup: 2,
  provider_rate: 1200,
  kora_rate: 1250,
  last_updated: new Date('2023-01-01T10:00:00Z').toISOString(),
  icon: 'arrowRight' as TIconNames,
  icon_color: '#000',
  status: 'active'
};

const renderWithMockIndex = (ui: React.ReactElement) => {
  return render(<MockIndex>{ui}</MockIndex>);
};

const apiCallSpy = vi.fn();

beforeEach(() => {
  server.use(
    http.patch('/admin/conversions/currency-pair/markup', async ({ request }) => {
      const data = await request.json();
      apiCallSpy(data);
      return HttpResponse.json({ data }, { status: 200 });
    })
  );
});

describe('CurrencyPairModal - Visual and API Tests', () => {
  test('is accessible', async () => {
    const { container } = renderWithMockIndex(
      <CurrencyPairModal action="edit" close={vi.fn()} currencyPair={defaultCurrencyPair} isMarkupLoading={false} />
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('displays loading placeholders when isMarkupLoading is true and rates are undefined', () => {
    const currencyPairWithNoRates = { ...defaultCurrencyPair, provider_rate: undefined, kora_rate: undefined };

    renderWithMockIndex(<CurrencyPairModal action="edit" close={vi.fn()} currencyPair={currencyPairWithNoRates} isMarkupLoading={true} />);

    expect(screen.getAllByTestId('loading-placeholder')).toHaveLength(2);
  });

  test('does NOT display loading placeholders when isMarkupLoading is false', () => {
    renderWithMockIndex(<CurrencyPairModal action="edit" close={vi.fn()} currencyPair={defaultCurrencyPair} isMarkupLoading={false} />);

    expect(screen.queryAllByTestId('loading-placeholder')).toHaveLength(0);
  });

  test('calls PATCH /admin/conversions/currency-pair/markup when saving updated markup', async () => {
    renderWithMockIndex(<CurrencyPairModal action="edit" close={vi.fn()} currencyPair={defaultCurrencyPair} isMarkupLoading={false} />);

    const input = screen.getByLabelText('Markup');
    fireEvent.change(input, { target: { value: '3' } });

    const continueBtn = screen.getByTestId('markup-edit-continue');
    fireEvent.click(continueBtn);

    const saveBtn = await screen.getByTestId('markup-edit-save');
    fireEvent.click(saveBtn);

    await waitFor(() => {
      expect(apiCallSpy).toHaveBeenCalledWith({
        to_currency: 'NGN',
        from_currency: 'USD',
        base_markup: 3
      });
    });
  });
});
