import React, { useEffect, useState } from 'react';
import { useQuery } from 'react-query';

import { useFeedbackHandler, useSearchQuery } from '+hooks';
import useLargeExportDownloader from '+hooks/useLargeExportDownloader';
import APIRequest from '+services/api-services';
import LargeExportModal from '+shared/LargeExportModal';
import Table from '+shared/Table';
import useStore from '+store';
import { conversionsTableDataType } from '+types';
import { APIDownload, capitalize, filteredOutObjectProperty, formatAmount, getDate, getTime, isAllowed, logError } from '+utils';

import arrowRight from '+assets/img/dashboard/arrow-right-black.svg';

import '../index.scss';

const api = new APIRequest();

const ConversionTransactions = () => {
  const { feedbackInit } = useFeedbackHandler();
  const [showLargeExportModal, setLargeExportModal] = useState(false);
  const { profile } = useStore(state => state);
  const searchQuery = useSearchQuery();

  const page = searchQuery.value.page || 1;
  const limit = searchQuery.value.limit || 10;

  const sortingParams = {
    ...filteredOutObjectProperty(searchQuery.value, ['page', 'limit', 'sorterType', 'tab'])
  };

  const feedbackInitValues = {
    message: '',
    type: 'danger',
    action: {
      action: () => refetch(),
      name: 'Try again'
    },
    title: undefined,
    callback: undefined,
    statusCode: undefined,
    isActive: false,
    isClosable: true,
    componentLevel: true
  };

  const { getDownload } = useLargeExportDownloader('Conversions');

  useEffect(() => {
    getDownload();
  }, []);

  const {
    data: walletData,
    refetch,
    isFetching
  } = useQuery(['SWAP_LIST', searchQuery.value], () => api.getSwaps(page, limit, sortingParams), {
    keepPreviousData: true,
    onError: () => {
      feedbackInit({
        ...feedbackInitValues,
        title: 'Conversions',
        message: 'There has been an error getting Conversions',
        type: 'danger'
      });
    }
  });

  const data = walletData?.data || [];
  const paging = walletData?.paging || {};

  const exportWalletSwaps = async (format: string, close: () => void, fieldsToExport: any) => {
    try {
      const res = await api.exportSwaps(format, fieldsToExport, sortingParams);
      if (res.status === 202) {
        setLargeExportModal(true);
      } else {
        const type = format === 'csv' ? 'csv' : 'xlsx';
        APIDownload(res, `Wallet Swap Report for ${getDate(Date.now())}`, type);
        feedbackInit({
          ...feedbackInitValues,
          title: 'Export Successful',
          message: (
            <>
              {' '}
              - Successfully exported <strong>{paging?.total_items} transactions.</strong>
            </>
          ),
          type: 'success'
        });
      }
      close();
    } catch (error) {
      logError(error);
      feedbackInit({
        ...feedbackInitValues,
        title: 'Export Failed',
        message: `There has been an error downloading your transactions`,
        type: 'danger'
      });
    }
  };

  const Conversions = {
    className: '--swaps-table',
    rowKey: 'reference',
    rowURL: '/dashboard/currency-conversions',
    emptyStateHeading: 'No records yet',
    emptyStateMessage: 'There are no wallet swap transactions yet.',
    annotations: 'transaction(s)',
    fields: (item: conversionsTableDataType) => ({
      data: {
        'Date / Time': (
          <>
            <span>{getDate(item.transaction_date)}</span>
            <span className="annotation">{getTime(item.transaction_date)}</span>
          </>
        ),
        transaction_id: <span className="trxn-id">{item.reference}</span>,
        merchant: <span className="merch-name">{capitalize(item.account?.name || 'Not Available')}</span>,
        'currency pair': (
          <>
            <span className="currency-pair">
              <span className="annotation">{item.source_currency}</span>
              <img sizes="sm" width={10} height={10} src={arrowRight} alt="arrow right icon" aria-hidden />
              <span className="annotation">{item.destination_currency}</span>
            </span>
          </>
        ),
        'amount converted': (
          <>
            <span>
              <strong>{formatAmount(item.source_amount)}</strong>
            </span>
            <span className="annotation">{item.source_currency}</span>
          </>
        ),
        'converted to': (
          <>
            <span>
              <strong>{formatAmount(item.converted_amount)}</strong>
            </span>
            <span className="annotation">{item.destination_currency}</span>
          </>
        )
      }
    })
  };

  const tableDataKeys = Object.keys(Conversions.fields({}).data);

  return (
    <>
      <LargeExportModal close={() => setLargeExportModal(false)} email={profile.email} visible={showLargeExportModal} />
      <div className="row">
        <div className="col-sm-12">
          <div className="element-wrapper">
            <Table
              data={data}
              loading={isFetching}
              className={`${Conversions.className || ''}`}
              tableWrapperClassName="table-responsive"
              paginationPage
              renderFields
              hasPagination
              tableHeadings={tableDataKeys}
              annotation={Conversions.annotations}
              current={parseInt(page, 10)}
              rowKey={Conversions.rowKey}
              rowURL={Conversions.rowURL}
              pageSize={paging?.page_size || 0}
              totalItems={paging?.total_items || 0}
              limitAction={(limit: number) => searchQuery.setQuery({ limit: String(limit) })}
              actionFn={(current: number) => searchQuery.setQuery({ page: String(current) })}
              emptyStateHeading={Conversions.emptyStateHeading || ''}
              emptyStateMessage={Conversions.emptyStateMessage || ''}
              filterType="wallet_swap"
              filterHasAdvancedFilter={false}
              filterExportAction={exportWalletSwaps}
              filterKeywordPlaceholder="Search Transaction"
              filterQueryIDPlaceholder="Payout ID"
              filterTotalCount={paging?.total_items}
              filterShowExport={isAllowed}
            >
              {Conversions.fields}
            </Table>
          </div>
        </div>
      </div>
    </>
  );
};
export default ConversionTransactions;
