import React, { useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { useFormik } from 'formik';

import { useFeedbackHandler } from '+hooks';
import APIRequest from '+services/api-services';
import { CurrencyType, FeeType } from '+types';
import { logError } from '+utils';

import Icon from '../Icons';
import InputGroup from '../InputGroup';
import Modal from '../Modal';

interface IWalletFundingFeeWaiverModal {
  onClose: () => void;
  type: 'global' | 'merchant';
  feeType: FeeType;
  feeEnabled: boolean;
  currency: CurrencyType;
  koraId: number;
  amount: number;
  merchantName: string;
}
type ModalStepType = 'set_waiver' | 'confirm_waiver';
type FormValuesType = {
  feeType: FeeType;
  amount: string;
  waiverApplied: boolean;
};

const api = new APIRequest();

export default function WalletFundingFeeWaiverModal({
  merchantName,
  onClose,
  type,
  currency,
  koraId,
  feeEnabled,
  amount,
  feeType
}: IWalletFundingFeeWaiverModal) {
  const [step, setStep] = useState<ModalStepType>('set_waiver');
  const queryClient = useQueryClient();
  const { feedbackInit, closeFeedback } = useFeedbackHandler();
  const isMerchantContext = type === 'merchant';

  const waiveFeeForMerchant = useMutation(
    ({ feeEnabled, feeType, amount }: { feeEnabled: boolean; feeType: FeeType; amount: number }) =>
      api.waiveWalletFundingFeeForMerchant({
        accountId: koraId,
        currency,
        type: feeType,
        amount,
        feeEnabled
      }),
    {
      onSuccess: () => queryClient.invalidateQueries(['MERCHANT_DETAILS']),
      onError: error => {
        logError(error);
        feedbackInit({
          message: error.response?.data?.message || 'Action was unsuccessful',
          type: 'danger',
          componentLevel: true
        });
      },
      onSettled: () => {
        setTimeout(() => {
          closeFeedback();
        }, 5000);
      }
    }
  );

  const formik = useFormik<FormValuesType>({
    initialValues: {
      feeType: feeType,
      amount: amount.toString(),
      waiverApplied: !feeEnabled
    },
    validate: formValues => {
      let errors: Partial<Record<keyof FormValuesType, string>> = {};

      if (step === 'set_waiver') {
        if (!formValues.waiverApplied) {
          if (!formValues.amount) {
            errors.amount = 'An amount is required';
          } else if (Number(formValues.amount) < 0) {
            errors.amount = 'Must be greater than or equal to 0';
          }
          if (!formValues.feeType) {
            errors.feeType = 'A fee type is required';
          }
        }
      }
      return errors;
    },
    onSubmit: async formValues => {
      switch (step) {
        case 'set_waiver':
          setStep('confirm_waiver');
          break;
        case 'confirm_waiver':
          await waiveFeeForMerchant.mutateAsync({
            feeEnabled: !formValues.waiverApplied,
            amount: parseFloat(formValues.amount.replace(/,/g, '')),
            feeType: formValues.feeType
          });
          break;
        default:
      }
    }
  });
  const isWaived = formik.values.waiverApplied;

  const modalStepProps: Record<ModalStepType, Partial<React.ComponentProps<typeof Modal>>> = {
    set_waiver: {
      heading: 'Customize Issuing Wallet Funding Fee',
      description: `Set a fee to charge ${isMerchantContext ? 'this merchant' : 'merchants'} whenever they fund their card issuing wallet, or choose to waive the fee for them entirely.`,
      headerBottomBorder: true,
      content: (
        <div className="p-2">
          <div className="d-flex align-items-start" style={{ gap: '0.5rem', paddingBottom: '1rem' }}>
            <input type="checkbox" className="mt-1" {...formik.getFieldProps('waiverApplied')} id="waiverApplied" checked={isWaived} />
            <div>
              <label htmlFor="waiverApplied" className="mt-0">
                Waive wallet funding fee
              </label>
              <p
                className="mb-0 mt-1"
                style={{
                  color: '#414F5F',
                  opacity: 0.7,
                  fontSize: '0.8rem'
                }}
              >
                {isMerchantContext ? 'This merchant' : 'Merchants'} will not be charged when they fund their wallet.
              </p>
            </div>
          </div>
          <fieldset disabled={formik.values.waiverApplied} className="mt-0" style={{ paddingTop: '1rem', borderTop: '1px solid #DDE2EC' }}>
            <div>
              <label className="mt-0 mb-1" htmlFor="feeType">
                Fee Type
              </label>
              <select {...formik.getFieldProps('feeType')} id="feeType" className="form-control">
                <option value="percentage">Percentage</option>
                <option value="flat">Flat</option>
              </select>
            </div>
            <div className="mt-2">
              <label htmlFor="amount" className="mb-1">
                Amount
              </label>
              <InputGroup errorMessage={formik.touched.amount ? formik.errors.amount : ''}>
                <InputGroup.Input id="amount" {...formik.getFieldProps('amount')} />
                <InputGroup.Addon>{formik.values.feeType === 'percentage' ? '%' : 'USD'}</InputGroup.Addon>
                <InputGroup.Error />
              </InputGroup>
            </div>
          </fieldset>
        </div>
      ),
      size: 'md',
      secondButtonActionIsTerminal: false
    },
    confirm_waiver: {
      heading: <div style={{ maxWidth: '23ch' }}>Confirm Issuing Wallet Funding Fee {isWaived ? 'Waiver' : 'Update'}</div>,
      description: `Kindly confirm that you want to ${isWaived ? `proceed with waiving issuing wallet funding fee for ${merchantName}.` : 'apply the modification(s) to this configuration'}`,
      secondButtonText: 'Yes, Confirm',
      firstButtonText: 'Back',
      firstButtonAction: () => setStep('set_waiver'),
      completedDescription: `You have successfully ${isWaived ? `waived issuing wallet funding fee for ${isMerchantContext ? merchantName : 'merchants'}` : 'made changes to this configuration'}.`,
      size: 'sm',
      equalFooterBtn: true,
      content: (
        <div className="px-2 d-flex">
          <Icon name="infoSolid" height={16} width={16} className="mt-1 flex-shrink-0" fill="#2376F3" />
          <span className="font-weight-bold" style={{ color: '#2376F3' }}>
            Note that {isMerchantContext ? 'this merchant' : 'merchants'}
            {isWaived
              ? ' will not be charged whenever'
              : ` will be charged ${formik.values.amount}${formik.values.feeType === 'percentage' ? '%' : ` ${currency}`} of their funding amount every time`}{' '}
            they fund their card issuing wallet.
          </span>
        </div>
      )
    }
  };

  return <Modal {...modalStepProps[step]} secondButtonAction={formik.submitForm} secondaryCompletedModal close={onClose} />;
}
