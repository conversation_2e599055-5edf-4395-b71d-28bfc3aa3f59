import React, { useState } from 'react';

import LimitDataTable from '+containers/Dashboard/Shared/LimitDataTable';
import { useSetUserAccess } from '+hooks';
import { CurrencyType } from '+types';
import { isAllowed } from '+utils';

import WalletFundingFeeWaiverModal from './WalletFundingFeeWaiverModal';

import './WalletFundingFeeWaiverModal.scss';

interface ISubcriptionFeeWaiverSettings {
  type: 'global' | 'merchant';
  feeType: 'percentage' | 'flat';
  feeAmount: number;
  feeEnabled: boolean;
  koraId: number;
  currency: CurrencyType;
  merchantName: string;
}

export default function WalletFundingFeeWaiverSettings({
  merchantName,
  type,
  koraId,
  currency,
  feeAmount,
  feeEnabled,
  feeType
}: ISubcriptionFeeWaiverSettings) {
  const [modalIsOpen, setModalIsOpen] = useState(false);
  const isGlobalContext = type === 'global';
  const userAccess = useSetUserAccess();
  const canWaiveFee = isAllowed(userAccess, ['card_issuance_wallet_funding_fee.update']);

  return (
    <div className={isGlobalContext ? 'wallet-funding-fee-waiver-settings' : ''}>
      <LimitDataTable
        aria-label="edit issuing wallet funding fee"
        title="Issuing Wallet Funding Fee"
        hideAction={!canWaiveFee}
        description={
          isGlobalContext
            ? 'Configure what merchants are charged when they fund their card issuing wallet. You can also decide to waive or apply this fee.'
            : 'This is what we charge merchants whenever they fund their card issuing wallet.'
        }
        actionText="Customize"
        headings={['Description', 'Fee Type', 'Amount', 'Status']}
        onActionClick={() => setModalIsOpen(true)}
        data={[
          [
            <div className="font-weight-light">Wallet funding transaction fee</div>,
            <span className="text-capitalize">{feeType}</span>,
            `${feeAmount}${feeType === 'flat' ? ` ${currency}` : '%'}`,
            feeEnabled ? 'Applied' : 'Waived'
          ]
        ]}
        textAlign="right"
      />
      {modalIsOpen && (
        <WalletFundingFeeWaiverModal
          merchantName={merchantName}
          feeType={feeType}
          amount={feeAmount}
          feeEnabled={feeEnabled}
          onClose={() => setModalIsOpen(false)}
          type={type}
          currency={currency}
          koraId={koraId}
        />
      )}
    </div>
  );
}
