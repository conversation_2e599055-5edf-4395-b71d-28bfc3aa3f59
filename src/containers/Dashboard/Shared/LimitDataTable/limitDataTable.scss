.limit-data-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  background-color: #f9fbfd;
  padding: 1.5rem;
  border-radius: 8px;
  position: relative;

  & > * + * {
    margin-top: 15px;
  }

  .table-container {
    overflow-x: auto;
    width: 100%;
  }

  .table-scroll-wrapper::-webkit-scrollbar {
    height: 8px;
    background-color: #f9fbfd;
  }

  .table-scroll-wrapper::-webkit-scrollbar-thumb {
    background-color: #f9fbfd;
    border-radius: 4px;
  }

  .table-scroll-wrapper::-webkit-scrollbar-thumb:hover {
    background-color: #f9fbfd;
  }

  .table[role='table'] {
    display: grid;
    grid-template-columns: var(--zeroth-column-width) repeat(calc(var(--num-of-columns) - 1), 1fr);
    width: 100%;
    border-spacing: 0;
    margin-top: 1.5rem;
    background-color: #f9fbfd;

    @media (max-width: 1440px) {
      grid-template-columns: 30% repeat(calc(var(--num-of-columns) - 1), 1fr);
    }
  }

  .table__head[role='rowgroup'],
  .table__body[role='rowgroup'],
  .table__head-row[role='row'],
  .table__body-row[role='row'] {
    display: contents;
  }

  .table__head-col[role='columnheader'],
  .table__body-col[role='rowheader'],
  .table__body-col[role='cell'] {
    padding: 0.7rem 1rem;
    text-align: left;
    border-top: 1px solid #dde2ec;
    border-bottom: none;
    align-self: baseline;
    background-color: inherit;
  }

  [role='rowheader'],
  [role='columnheader'] {
    color: #414f5f;
    font-weight: 500;
  }

  .table__head[role='rowgroup'] .table__head-col[role='columnheader'] {
    border-top: none;
  }

  .table__head-col[role='columnheader'] {
    font-weight: 500;
    white-space: nowrap;
  }

  .table__head-col[role='columnheader']:not(:first-child) {
    text-align: left;
  }

  .table__head-col[role='columnheader']:first-child {
    padding-left: 0;
    position: sticky;
    left: 0;
    z-index: 2;
    background-color: #f9fbfd;
  }

  .table__body-col[role='rowheader'] {
    font-weight: 500;
    color: #374151;
    text-align: left;
    padding-left: 0;
    white-space: nowrap;
    position: sticky;
    left: 0;
    z-index: 1;
    background-color: #f9fbfd;
  }

  .table__body-col[role='cell'] {
    color: #94a7b7;
    font-weight: 500;
    text-align: left;
  }

  .table__head-col:empty,
  .table__body-col:empty {
    padding: 0;
  }
}

.config-section {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 30px;
}

.config-header {
  & > * + * {
    margin-top: 8px !important;
  }

  &__title {
    font-weight: 500;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0px;
    color: #292b2c;
    margin-bottom: 0;
  }

  &__description {
    font-weight: 400;
    font-size: 15px;
    line-height: 20px;
    letter-spacing: 0px;
    color: #414f5f;
    opacity: 0.7;
    margin-bottom: 0;
  }

  &__actions-link {
    font-weight: 500 !important;
    font-size: 16px !important;
    line-height: 23px !important;
    letter-spacing: -0.3px !important;
  }
}
