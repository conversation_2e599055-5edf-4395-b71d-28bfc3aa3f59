import React from 'react';

import './limitDataTable.scss';

interface ILimitDataTable {
  title: React.ReactNode;
  description: React.ReactNode;
  actionText: React.ReactNode;
  onActionClick: () => void;
  actionIsDisabled?: boolean;
  hideAction?: boolean;
  headings: Array<React.ReactNode>;
  data: Array<Array<React.ReactNode>>;
  textAlign?: 'left' | 'right';
  zerothColumnWidth?: string;
  'aria-label': string;
}

export default function LimitDataTable({
  headings = [],
  data,
  title,
  description,
  onActionClick,
  actionText,
  actionIsDisabled,
  textAlign,
  zerothColumnWidth = '50%',
  'aria-label': ariaLabel,
  hideAction = false
}: ILimitDataTable) {
  return (
    <section className="limit-data-table">
      <div className="config-section">
        <div className="config-header">
          <p className="config-header__title">{title}</p>
          <p className="config-header__description">{description}</p>
        </div>
        {!hideAction && (
          <div className="config-header__actions">
            <button
              aria-label={ariaLabel}
              onClick={onActionClick}
              type="button"
              className="text-decoration-none btn-link config-header__actions-link"
              disabled={actionIsDisabled}
            >
              {actionText}
            </button>
          </div>
        )}
      </div>
      <div className="table-container">
        <div
          role="table"
          className="table"
          style={{
            ['--num-of-columns' as string]: data[0].length,
            ['--zeroth-column-width' as string]: zerothColumnWidth
          }}
        >
          <div role="rowgroup" className="table__head">
            <div role="row" className="table__head-row">
              {headings.map((heading, index) => (
                <div role="columnheader" className="table__head-col" key={index} style={{ textAlign: index === 0 ? 'left' : textAlign }}>
                  {heading}
                </div>
              ))}
            </div>
          </div>
          <div role="rowgroup" className="table__body">
            {data.map((row, index) => (
              <div key={index} role="row" className="table__body-row">
                {row.map((column, index) => {
                  const role = index === 0 ? 'rowheader' : 'cell';
                  return (
                    <div key={index} role={role} className="table__body-col" style={{ textAlign: index === 0 ? 'left' : textAlign }}>
                      {column}
                    </div>
                  );
                })}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
