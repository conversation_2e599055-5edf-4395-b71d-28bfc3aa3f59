import React, { useCallback, useEffect, useMemo, useState } from 'react';

import Icon from '+containers/Dashboard/Shared/Icons';

import Modal from '../Modal';

import './index.scss';

/**
 * BatchSearchViewerModal component displays a modal with a list of reference IDs.
 * It is used to show the results of a batch search query.
 *
 * @param {Object} props - The properties for the component.
 * @param {boolean} props.visible - Determines if the modal is visible.
 * @param {Function} props.close - Function to close the modal.
 * @param {string | string[]} props.ids - The reference IDs to display.
 * @param {string} [props.heading] - Optional heading for the modal.
 * @param {string} [props.description] - Optional description for the modal.
 */

const BatchSearchViewerModal = ({
  visible,
  close,
  ids,
  heading = 'Reference IDs To Search',
  description,
  cancelIdAction
}: {
  visible: boolean;
  close: () => void;
  ids: string | string[];
  heading?: string;
  description?: string;
  cancelIdAction: (id: string) => void;
}): JSX.Element => {
  const [localIds, setLocalIds] = useState<string[]>([]);

  useEffect(() => {
    if (visible) {
      setLocalIds(Array.isArray(ids) ? ids : [ids]);
    }
  }, [visible, ids]);

  useEffect(() => {
    if (visible && localIds.length === 0) {
      close();
    }
  }, [localIds, visible, close]);

  const handleRemoveId = useCallback(
    (id: string) => {
      setLocalIds(prev => prev.filter(item => item !== id));
      cancelIdAction(id);
    },
    [cancelIdAction]
  );

  const FormattedIdsView = useMemo(
    () => (
      <div className="batch-viewer-wrapper">
        <ul className="formatted-ids-list">
          {localIds.map(id => (
            <li key={id} className="id-item-card">
              <span className="id-text">{id}</span>
              <div
                role="button"
                tabIndex={0}
                aria-label={`Remove ID ${id}`}
                onClick={() => handleRemoveId(id)}
                onKeyDown={e => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    handleRemoveId(id);
                    e.preventDefault();
                  }
                }}
                className="remove-id-div"
                data-testid={`remove-id-${id}`}
              >
                <Icon name="circledClose" fill="#AABDCE" width={15} height={15} />
              </div>
            </li>
          ))}
        </ul>
      </div>
    ),
    [localIds, handleRemoveId]
  );

  return (
    <Modal
      close={close}
      size={'lg'}
      heading={heading}
      description={description}
      content={FormattedIdsView}
      visible={visible}
      firstButtonText="Close"
      hideSecondButton
      headerBottomBorder
      footerButtonWidthIsAuto
      isScrollable
    />
  );
};

export default BatchSearchViewerModal;
