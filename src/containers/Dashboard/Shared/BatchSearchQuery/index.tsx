import React, { ReactElement, useEffect, useState } from 'react';

import Icon from '+containers/Dashboard/Shared/Icons';
import { IBatchQueryProps } from '+types';

import './index.scss';

const BatchSearchQueryInput = ({
  ids,
  setIds,
  handleBatchCancelId,
  handleClearAllBatchQueryIds,
  placeholder = '',
  idCountLimit = 5,
  idRefLabel = 'Reference',
  idTrimLength = 3
}: IBatchQueryProps & {
  ids: string[];
  setIds: (ids: string[]) => void;
  handleBatchCancelId: (id: string) => void;
  handleClearAllBatchQueryIds?:()=>void;
}) => {
  const idCountLimitWarning = idCountLimit - Math.ceil((10 / 100) * idCountLimit);
  const [inputId, setInputId] = useState<string>('');
  const [error, setError] = useState<ReactElement | string>('');

  const deleteIdFn = (id: string) => {
    setIds(ids.filter(i => i !== id));
    handleBatchCancelId(id);
  };

  const editIdFn = (id: string) => {
    setInputId(id);
    deleteIdFn(id);
  };

  const scrollIdsWrapperToEnd = () => {
    const idsContainer = document.getElementById('inputted-ids-wrapper');

    idsContainer?.scrollTo?.({
      left: idsContainer.scrollWidth,
      behavior: 'smooth'
    });
  };

  const addId = (str: string) => {
    const val = str.trim();

    if (ids.length >= idCountLimit) {
      setError(
        <>
          You can only search up to{' '}
          <strong>
            {idCountLimit} {idRefLabel}
          </strong>{' '}
          at once
        </>
      );
      return;
    }

    if (ids.map(id => id.toLowerCase()).includes(val.toLowerCase())) {
      setError(`${idRefLabel} already added`);
      return;
    }

    if (val) {
      setInputId('');
      setIds([...ids, val]);
      setTimeout(() => scrollIdsWrapperToEnd(), 50);
    }
  };

  const trimInput = ({ value, trimLength }: { value: string | null | undefined; trimLength: number }): string => {
    if (!value || value.trim() === '') {
      return '';
    }
    const trimmedValue = value.trim();

    if (trimmedValue.length > trimLength) {
      return trimmedValue.slice(0, trimLength) + '...';
    }

    return trimmedValue;
  };

  useEffect(() => {
    if (error && !inputId) setError('');
    if (ids.length !== idCountLimit && !ids.map(id => id.toLowerCase()).includes(inputId.toLowerCase())) setError('');
  }, [ids.length, inputId]);

  return (
    <div className="batch-query-wrapper">
      <div
        className={`${ids.length > 0 ? 'input-count' : 'input-count-inactive'} ${ids.length >= idCountLimitWarning ? 'input-count-limit' : ''}`}
        aria-live="polite"
        aria-label={`IDs entered: ${ids.length} out of ${idCountLimit}`}
        data-testid="input-id-count"
      >
        {ids.length}/{idCountLimit}
      </div>

      <div className="batch-query-id-wrapper" id="inputted-ids-wrapper" role="list" aria-label="List of entered IDs">
        {ids.map(id => {
          return (
            <div key={id} className="inputted-id" data-testid="inputted-id" role="listitem">
              <button className="id" onClick={() => editIdFn(id)} aria-label={`Edit ID ${id}`}>
                {trimInput({ value: id, trimLength: idTrimLength })}
              </button>
              <hr />
              <button onClick={() => deleteIdFn(id)} className="btn delete-id" aria-label={`Delete ID ${id}`} data-testid="delete-id-count">
                <Icon name="cancel" stroke="currentColor" width={16} height={16} />
              </button>
            </div>
          );
        })}
      </div>

      <input
        data-testid="input-processor-ids"
        aria-label="input-processor-ids"
        aria-describedby="inputted-ids-wrapper"
        placeholder={placeholder}
        value={inputId}
        maxLength={50}
        onKeyDown={e => {
          if (e.key === 'Backspace' && !inputId) {
            setIds(ids.slice(0, -1));
          }
        }}
        onChange={e => {
          const value = e.target.value;

          if (value.endsWith(' ')) addId(value);
          else setInputId(value);
        }}
      />

      <button
        disabled={!ids.length}
        onClick={() => {
          setIds([]);
          setInputId('');
          if(handleClearAllBatchQueryIds){
            handleClearAllBatchQueryIds();
          }
        }}
        className="clear-processor-id"
        aria-label="Clear all entered processor IDs"
      >
        <Icon name="close" width={16} height={16} />
      </button>

      {error && (
        <div className="error-msg" role="alert" aria-live="assertive" data-testid="error-message-id">
          <Icon name="warningOrange" fill="red" height={12} width={12} />
          {error}
        </div>
      )}
    </div>
  );
};

export default BatchSearchQueryInput;
