import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';

import BatchSearchViewerModal from '../BatchSearchViewerModal';

const BatchSearchViewerModalWrapper = ({
  ids,
  close,
  visible,
  cancelIdAction
}: {
  ids: string[];
  close: () => void;
  visible: boolean;
  cancelIdAction: () => void;
}) => {
  return (
    <MockIndex>
      {' '}
      <BatchSearchViewerModal ids={ids} close={close} visible={visible} cancelIdAction={cancelIdAction} />
    </MockIndex>
  );
};
describe('Batch Viewer Modal', () => {
  test('Batch Viewer Modal is accessible', async () => {
    const { container } = render(
      <BatchSearchViewerModalWrapper ids={['2', '1']} close={() => vi.fn()} visible={true} cancelIdAction={() => vi.fn()} />
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  test('should render the component', () => {
    render(<BatchSearchViewerModalWrapper ids={['2', '1']} close={() => vi.fn()} visible={true} cancelIdAction={() => vi.fn()} />);
    expect(screen.getByText(/Reference IDs To Search/g)).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
  });
  test('the cancel action is work', async () => {
    render(<BatchSearchViewerModalWrapper ids={['2', '1']} close={() => vi.fn()} visible={true} cancelIdAction={() => vi.fn()} />);
    const removeButton = screen.getByTestId('remove-id-2');
    expect(removeButton).toBeInTheDocument();
    await userEvent.click(removeButton);
    await waitFor(() => {
      expect(screen.queryByText('2')).not.toBeInTheDocument();
    });
  });
});
