import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import BatchSearchQueryInput from '../index';

const BatchQueryWrapper = ({ ids, setIds }: { ids: string[]; setIds: (ids: string[]) => void }) => {
  return <BatchSearchQueryInput ids={ids} setIds={setIds} idCountLimit={5} placeholder="Search" idTrimLength={3} idRefLabel="Reference" handleBatchCancelId={vi.fn()}/>;
};
describe('Batch Query Search', () => {
  test('Batch Query Search is accessible', async () => {
    const { container } = render(<BatchSearchQueryInput ids={[]} setIds={() => vi.fn()} handleBatchCancelId={vi.fn()}/>);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  test('should render the component', () => {
    render(
      <BatchSearchQueryInput
        ids={[]}
        setIds={() => vi.fn()}
        placeholder="Search"
        idCountLimit={5}
        idRefLabel="Reference"
        idTrimLength={3}
        handleBatchCancelId={vi.fn()}
      />
    );
    expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
    expect(screen.getByText('0/5')).toBeInTheDocument();
  });
  test('should show error when limit is reached', async () => {
    render(
      <BatchSearchQueryInput
        ids={['1', '2']}
        setIds={() => vi.fn()}
        placeholder="Search"
        idCountLimit={2}
        idRefLabel="Reference"
        idTrimLength={3}
        handleBatchCancelId={vi.fn()}
      />
    );
    const input = screen.getByPlaceholderText('Search');
    await userEvent.type(input, '3 ');
    await waitFor(() => {
      const errorElement = screen.getByTestId('error-message-id');
      expect(errorElement).toHaveTextContent(/you can only search up to 2 reference/i);
    });
  });
  test('should show error when id already exists', async () => {
    render(
      <BatchSearchQueryInput
        ids={['1', '2']}
        setIds={() => vi.fn()}
        placeholder="Search"
        idCountLimit={5}
        idRefLabel="Reference"
        idTrimLength={3}
        handleBatchCancelId={vi.fn()}
      />
    );
    const input = screen.getByPlaceholderText('Search');
    await userEvent.type(input, '2 ');
    await waitFor(() => {
      const errorElement = screen.getByTestId('error-message-id');
      expect(errorElement).toHaveTextContent(/Reference already added/i);
    });
  });
  test('should display the count when id is added', async () => {
    const ids = ['1'];
    const setIds = vi.fn();
    render(<BatchQueryWrapper ids={ids} setIds={setIds} />);
    const input = screen.getByPlaceholderText('Search');
    await userEvent.type(input, '2 '); // type with trailing space to trigger add() function
    await waitFor(() => {
      expect(setIds).toHaveBeenCalledWith(['1', '2']);
    });
  });
  test('removes an ID when delete button is clicked', async () => {
    const ids = ['1', '2'];
    const setIds = vi.fn();
    render(<BatchQueryWrapper ids={ids} setIds={setIds} />);

    await waitFor(() => {
      expect(screen.getByTestId('input-id-count')).toHaveTextContent('2/5');
    });

    expect(screen.getAllByTestId('inputted-id')).toHaveLength(2);

    const deleteButtons = screen.getAllByTestId('delete-id-count');
    await userEvent.click(deleteButtons[0]);

    await waitFor(() => {
      expect(setIds).toHaveBeenCalledWith(['2']);
    });
  });
});

