.batch-query-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-left: 0.75rem;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 2px solid #dde2ec;
  border-radius: 4px;
  transition:
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
  input {
    border: 0;
    outline: 0;
    padding-right: 0.75rem;
    padding-left: 0.75rem;
    width: 100%;
    flex-grow: 1;
    flex-shrink: 1;
  }
  .input-count,
  .input-count-inactive {
    border-right: 1px solid #e0e0e0;
    color: #a9afbc;
    cursor: default;
    padding-right: 0.75rem;
    flex-shrink: 0;
    flex-grow: 0;
  }

  .input-count-inactive {
    color: #a9afbc73;
  }

  .input-count-limit {
    color: #ff4d4f;
    flex-shrink: 0;
    flex-grow: 0;
  }
  > input::placeholder {
    color: #a9afbc;
  }
  .clear-processor-id {
    &:disabled {
      opacity: 0.5;
    }
  }
  .error-msg {
    align-items: center;
    bottom: -1.2rem;
    color: #f32345;
    display: flex;
    gap: 0.5rem;
    margin: 0.25rem 0 0;
    position: absolute;
  }
  .batch-query-id-wrapper {
    flex-grow: 2;
    flex-shrink: 1;

    display: flex;
    gap: 0.5rem;
    margin: 0 0.25rem 0 0.75rem;
    max-width: 13rem;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .inputted-id {
      align-items: center;
      background: #dde2ec;
      border-radius: 8px;
      color: #3e4b5b;
      display: flex;
      gap: 0.25rem;
      padding: 0.1rem 0.5rem;

      .id {
        cursor: text;
        color: inherit;
        white-space: nowrap;
      }

      hr {
        margin: 0;
        background-color: #7d7d7d3b;
        height: 12px;
        width: 1px;
      }

      .delete-id {
        margin: 0;
        padding: 0;
      }
    }
  }
}

.batch-viewer-wrapper {
  width: 100%;
  box-sizing: border-box;
  padding: 1rem;

  .formatted-ids-list {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 0.5rem;
  }

  .id-item-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0.75rem;
    background-color: #f1f6fa;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    color: #414f5f;
    box-sizing: border-box;
    max-width: 100%;
    width: fit-content;
    white-space: nowrap;
    gap: 0.5rem;
  }

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .remove-id-div {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    svg {
      transition: transform 0.2s ease;
    }

    &:hover svg {
      transform: scale(1.1);
    }
  }
}
