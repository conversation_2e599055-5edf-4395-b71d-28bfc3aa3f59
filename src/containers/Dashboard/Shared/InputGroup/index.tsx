import React, {
  createContext,
  Dispatch,
  ForwardRefRenderFunction,
  HTMLAttributes,
  ReactNode,
  SetStateAction,
  useContext,
  useEffect,
  useId,
  useMemo,
  useState
} from 'react';
import { NumericFormatProps } from 'react-number-format';
import classNames from 'classnames';

import NumericInput from '../NumericInput';

import './InputGroup.scss';

type InputGroupContextType = {
  addonId: string | undefined;
  setAddonId: Dispatch<SetStateAction<string | undefined>>;
  errorId: string | undefined;
  setErrorId: Dispatch<SetStateAction<string | undefined>>;
  isInvalid: boolean;
  errorMessage: string | undefined | null;
};

const InputGroupContext = createContext<InputGroupContextType>({
  addonId: undefined,
  setAddonId: () => {},
  errorId: undefined,
  setErrorId: () => {},
  isInvalid: false,
  errorMessage: undefined
});

const InputComponent: ForwardRefRenderFunction<HTMLInputElement, NumericFormatProps> = ({ id, ...inputProps }, ref) => {
  const { addonId, errorId, isInvalid } = useContext(InputGroupContext);

  const describedBy = useMemo(() => {
    const ids = [addonId, errorId, inputProps['aria-describedby']].filter(Boolean).join(' ');
    return ids.length > 0 ? ids : undefined;
  }, [addonId, errorId, inputProps['aria-describedby']]);

  return (
    <NumericInput
      ref={ref}
      id={id}
      className="input-group__input"
      aria-describedby={describedBy}
      aria-invalid={isInvalid || undefined}
      {...inputProps}
    />
  );
};
const Input = React.forwardRef(InputComponent);
Input.displayName = 'InputGroup.Input';

function Addon({ children, className, ...otherProps }: React.HTMLAttributes<HTMLSpanElement>): JSX.Element {
  const { setAddonId } = useContext(InputGroupContext);
  const generatedId = useId();

  useEffect(() => {
    setAddonId(generatedId);
    return () => setAddonId(undefined);
  }, [generatedId, setAddonId]);

  return (
    <span id={generatedId} className={classNames('input-group__addon', className)} aria-hidden="true" {...otherProps}>
      {children}
    </span>
  );
}
Addon.displayName = 'InputGroup.Addon';

function Error(): JSX.Element | null {
  const { errorMessage, setErrorId } = useContext(InputGroupContext);
  const generatedId = useId();

  useEffect(() => {
    if (errorMessage) {
      setErrorId(generatedId);
    }
    return () => setErrorId(undefined);
  }, [errorMessage, generatedId, setErrorId]);

  if (!errorMessage) {
    return null;
  }

  return (
    <div id={generatedId} className="input-group__error">
      {errorMessage}
    </div>
  );
}
Error.displayName = 'InputGroup.Error';

interface InputGroupProps extends HTMLAttributes<HTMLDivElement> {
  children?: ReactNode;
  errorMessage?: string | null;
}

type InputGroupComponent = React.FC<InputGroupProps> & {
  Input: typeof Input;
  Addon: typeof Addon;
  Error: typeof Error;
};

const InputGroup: InputGroupComponent = ({ children, errorMessage = null, className, ...otherProps }) => {
  const [addonId, setAddonId] = useState<string | undefined>(undefined);
  const [errorId, setErrorId] = useState<string | undefined>(undefined);
  const isInvalid = !!errorMessage;

  const contextValue = useMemo<InputGroupContextType>(
    () => ({
      addonId,
      setAddonId: id => setAddonId(id),
      errorId,
      setErrorId: id => setErrorId(id),
      isInvalid: isInvalid,
      errorMessage: errorMessage
    }),
    [addonId, errorId, isInvalid, errorMessage]
  );

  const mainContentChildren: ReactNode[] = [];
  let errorContent: ReactNode | null = null;

  React.Children.forEach(children, child => {
    if (React.isValidElement(child)) {
      if (child.type === Error) {
        errorContent = child;
      } else if (child.type === Input || child.type === Addon) {
        mainContentChildren.push(child);
      }
    }
  });

  return (
    <InputGroupContext.Provider value={contextValue}>
      <div className={classNames('input-group', isInvalid && 'input-group--invalid', className)} {...otherProps}>
        {mainContentChildren}
      </div>
      {errorContent}
    </InputGroupContext.Provider>
  );
};

InputGroup.Input = Input;
InputGroup.Addon = Addon;
InputGroup.Error = Error;

export default InputGroup;
