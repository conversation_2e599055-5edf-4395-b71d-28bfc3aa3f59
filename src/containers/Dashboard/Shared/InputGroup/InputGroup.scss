.input-group {
  display: flex;
  align-items: stretch;
  width: 100%;
  border: 2px solid #dde2ec;
  border-radius: 4px;
  overflow: hidden;
  transition:
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  background-color: #fff;

  & > * {
    font-size: 0.9rem;
  }
}

.input-group:focus-within {
  border-color: #047bf8;
  border-width: 2px;
  outline: 0;
}

.input-group:has(> .input-group__input:disabled) .input-group__addon {
  background-color: #e9ecef;
}

.input-group__input {
  font-weight: 300;
  flex-grow: 1;
  min-width: 0;
  padding: 0.375rem 0.75rem;
  line-height: 1.5;
  color: #212529;
  background-color: transparent;
  border: none !important;
  outline: none;
  height: calc(1.5em + 0.75rem + 2px);
}

.input-group__input[type='number']::-webkit-outer-spin-button,
.input-group__input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.input-group__input[type='number'] {
  -moz-appearance: textfield;
  appearance: textfield;
}

.input-group__addon {
  display: flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  font-weight: 400;
  line-height: 1.5;
  color: #6c757d;
  text-align: center;
  white-space: nowrap;
  border-left: none;
  border-right: none;
}

.input-group--invalid {
  border-color: #ff5661;
}

.input-group--invalid:focus-within {
  border-color: #ff5661;
}

.input-group__error {
  color: #ff5661;
  font-size: 0.9rem;
  margin-top: 0.25rem;
  width: 100%;
}
