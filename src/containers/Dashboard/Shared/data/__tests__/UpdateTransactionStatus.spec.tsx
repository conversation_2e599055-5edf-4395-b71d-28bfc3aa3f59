import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';

import { UpdateTransactionStatus } from '../UpdateTransactionStatus';
import {FormStageType,IUpdateTransactionForm} from '+types';

const MockedUpdateTransactionStatus: React.FC<
  IUpdateTransactionForm & {
    stage: FormStageType;
    formValues: Record<string, string | boolean>;
    setState: (s: FormStageType | Record<string, string | boolean | Record<string, string | boolean>>) => void;
  }
> = ({ selectedRows, onCloseModal, clearSelection, setState, stage, formValues }) => {
  return (
    <MockIndex>
      <UpdateTransactionStatus
        onCloseModal={onCloseModal}
        selectedRows={selectedRows}
        currentStatus="processing"
        clearSelection={clearSelection}
        setState={setState}
        stage={stage}
        formValues={formValues}
        type='payins_transaction_update'
      />
    </MockIndex>
  );
};
describe('Payins Update Transaction Form', () => {
  test('Payins Update Transaction Form is accessible', async () => {
    const { container } = render(
      < MockedUpdateTransactionStatus
        onCloseModal={vi.fn()}
        selectedRows={['2']}
        currentStatus="processing"
        clearSelection={vi.fn()}
        setState={vi.fn()}
        stage={'stage_1'}
        formValues={{}}
      />
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('should render the component properly in stage_1', () => {
    render(
      < MockedUpdateTransactionStatus
        onCloseModal={vi.fn()}
        selectedRows={['2']}
        currentStatus="processing"
        clearSelection={vi.fn()}
        setState={vi.fn()}
        stage={'stage_1'}
        formValues={{}}
      />
    );
    expect(
      screen.getByText(/Updating these status\(es\) will reflect on all places where the transaction\(s\) are stored/i)
    ).toBeInTheDocument();
    expect(screen.getByLabelText(/Current Status/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/New Status/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Reason for update/i)).toBeInTheDocument();

    const checkbox = screen.getByRole('checkbox', {
      name: /Yes, I understand the implications of this action/i
    });
    expect(checkbox).toBeInTheDocument();
    expect(checkbox).not.toBeChecked();

    const continueButton = screen.getByRole('button', { name: /Continue/i });
    expect(continueButton).toBeInTheDocument();
    expect(continueButton).toBeDisabled();
  });
  test('should keep Continue button disabled when reason is less than 5 characters even if checkbox is checked', async () => {
    render(
      < MockedUpdateTransactionStatus
        onCloseModal={vi.fn()}
        selectedRows={['1']}
        currentStatus="processing"
        clearSelection={vi.fn()}
        setState={vi.fn()}
        stage={'stage_1'}
        formValues={{}}
      />
    );

    const reasonInput = screen.getByLabelText(/Reason for update/i);
    await userEvent.type(reasonInput, 'abc');

    const checkbox = screen.getByLabelText(/Yes, I understand the implications/i);
    await userEvent.click(checkbox);

    const continueButton = screen.getByRole('button', { name: /Continue/i });
    expect(continueButton).toBeDisabled();
  });
  test('should keep Continue button disabled when reason is valid but checkbox is not checked', async () => {
    render(
      < MockedUpdateTransactionStatus
        onCloseModal={vi.fn()}
        selectedRows={['1']}
        currentStatus="processing"
        clearSelection={vi.fn()}
        setState={vi.fn()}
        stage={'stage_1'}
        formValues={{}}
      />
    );

    const reasonInput = screen.getByLabelText(/Reason for update/i);
    await userEvent.type(reasonInput, 'valid reason');

    const continueButton = screen.getByRole('button', { name: /Continue/i });
    expect(continueButton).toBeDisabled();
  });
  test('should render stage_2 UI after clicking Continue button in stage_1', async () => {
    const { rerender } = render(
      < MockedUpdateTransactionStatus
        onCloseModal={vi.fn()}
        selectedRows={['1']}
        currentStatus="processing"
        clearSelection={vi.fn()}
        setState={vi.fn()}
        stage={'stage_1'}
        formValues={{}}
      />
    );
    const reasonInput = screen.getByLabelText(/Reason for update/i);
    await userEvent.type(reasonInput, 'valid reason');

    const checkbox = screen.getByLabelText(/Yes, I understand the implications/i);
    await userEvent.click(checkbox);

    const continueButton = screen.getByRole('button', { name: /Continue/i });
    expect(continueButton).toBeEnabled();

    await userEvent.click(continueButton);
    rerender(
      < MockedUpdateTransactionStatus
        onCloseModal={vi.fn()}
        selectedRows={['1']}
        currentStatus="processing"
        clearSelection={vi.fn()}
        setState={vi.fn()}
        stage={'stage_2'}
        formValues={{}}
      />
    );
    expect(screen.getByTestId('warning-tag')).toBeInTheDocument();
    expect(screen.getByText(/You are about to update the status/i)).toBeInTheDocument();
  });
});
