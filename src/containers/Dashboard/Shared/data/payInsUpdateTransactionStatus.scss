@import  '+styles/kpy-custom/variables';
.modal-content {
  svg {
    margin-right: 0.5rem;
  }
.cancel-payins-button{
    padding: 12px 34px;
    background-color: #EAF2FE ;
    color: #3E4B5B;
  }
  .action-botton-payins{
     padding: 12px 34px;
  }
  .stage-two-width-button{
width: 100%;
  }
  label {
    margin-top: 0.75rem;
    color: #414f5f;
    font-weight: 500;
  }

  #current-status {
    background: transparent !important;
  }

  .checkbox-container {
    display: flex;
    align-items: center;

    input {
      margin: 0 10px -8px 0;
    }
  }

  .select-action {
    position: relative;
    display: inline-block;
  }

  .additional-content {
    background: #fff8e1;
    padding: 1rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    padding: 10px 20px;
    gap: 8px;
  }
  .space-text {
    margin-top: 20px;
  }
  .banner-component {
    margin-top: 20px;
    margin-bottom: 20px;
    display: flex;
    width: 100%;
    height: 80px;
    padding-top: 20px;
    padding-bottom: 20px;
    padding-left: 20px;
    padding-right: 20px;
    background-color: #fff8e1;
    .banner-text {
      margin-left: 2px;
      overflow-wrap: break-word;
    }
    .icon-div-pay-out {
      padding-top: 8px;
    }
  }
  .banner-component-stage-two {
  margin-top: 20px;
margin-bottom: 20px;
display: flex;
width: 100%;
height: 80px;
padding: 20px;
align-items: center;
justify-content: center;
    background-color: #fff8e1;
    .banner-text {
      margin-left: 2px;
      text-align: justify;
      overflow-wrap: break-word;
    }
    .icon-div-pay-out {
      padding-top: 0px;
    }
    .text-warning-container {
    padding-top: 1rem;
    }
  }
  .pay-ins-hr {
    margin-left: -23px;
    margin-right: -23px;
  }
  .payins-button-group-right-1 {
  display: flex;
  justify-content: flex-end;
  gap: 5px;
}
.stage-three-wrapper{
   display: flex;
  flex-direction: column;
  align-items: center;
  .bold-text {
    font-family: Averta PE;
    font-size: 35px;
    font-weight: 700;
  }
  .small-text {
    font-family: Averta PE;
    font-size: 19px;
    font-weight: 400;
  }
  .dimissed-text {
    font-family: Averta PE;
  
    color: $kpyblue;
  }
}
}
