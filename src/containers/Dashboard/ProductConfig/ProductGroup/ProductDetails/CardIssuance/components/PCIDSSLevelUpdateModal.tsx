import React, { useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';

import Icon from '+containers/Dashboard/Shared/Icons';
import Modal from '+containers/Dashboard/Shared/Modal';
import { useFeedbackHandler } from '+hooks';
import APIRequest from '+services/api-services';
import { CardIssuanceDefaultLimitsResponseType, IModalProps, PCIDSSLevelType } from '+types';
import { formatWithCommas, logError } from '+utils';

import { pciDSSLevels } from '../constants/common';

type StepType = 'level_update' | 'confirm_level_update';

const apiRequest = new APIRequest();

export default function PCIDSSLevelUpdateModal({
  onClose,
  currentLevel = 'level_0',
  defaultLimits,
  reference = '',
  currentCardLimit,
  currentTransactionCountLimit
}: {
  onClose: () => void;
  currentLevel?: PCIDSSLevelType;
  defaultLimits?: CardIssuanceDefaultLimitsResponseType;
  reference?: string;
  currentTransactionCountLimit?: number | 'limitless';
  currentCardLimit?: number;
}) {
  const queryClient = useQueryClient();
  const [step, setStep] = useState<StepType>('level_update');
  const { feedbackInit, closeFeedback } = useFeedbackHandler();
  const [newLevel, setNewLevel] = useState(currentLevel);
  const [updateIsConfirmed, setUpdateIsConfirmed] = useState(false);
  const disableSubmit = step === 'level_update' ? currentLevel === newLevel : !updateIsConfirmed;
  const isPreviousLevel = currentLevel !== newLevel;

  const updatePCIDSSLevel = useMutation((level: PCIDSSLevelType) => apiRequest.updatePCIDSSLevel({ pciDSSLevel: level, reference }), {
    onSuccess: data => {
      feedbackInit({ message: data?.message, type: 'success' });
      queryClient.invalidateQueries(['MERCHANT_DETAILS']);
    },
    onError: error => {
      logError(error);
      feedbackInit({
        message: error.response?.data?.message || 'Action was unsuccessful',
        type: 'danger',
        componentLevel: true
      });
    },
    onSettled: () => {
      setTimeout(() => {
        closeFeedback();
      }, 5000);
    }
  });

  const handleSubmit = async () => {
    switch (step) {
      case 'level_update':
        setStep('confirm_level_update');
        break;
      case 'confirm_level_update':
        await updatePCIDSSLevel.mutateAsync(newLevel);
        break;
      default:
    }
  };

  const modalStepProps: Record<StepType, Omit<IModalProps, 'close'>> = {
    level_update: {
      secondButtonActionIsTerminal: false,
      heading: 'Update PCI DSS Level',
      description: 'Set the PCI DSS compliance level based on the merchant’s transaction volume and security requirements',
      content: (
        <div className="p-2">
          <div>
            <label htmlFor="merchantsPCIDSSLevel" className="mb-1 mt-0">
              Merchant's PCI DSS Level
            </label>
            <select
              id="merchantsPCIDSSLevel"
              value={newLevel}
              onChange={e => setNewLevel(e.target.value as PCIDSSLevelType)}
              className="form-control"
            >
              {pciDSSLevels.map(pciDSSLevel => (
                <option value={pciDSSLevel.value} key={pciDSSLevel.value}>
                  {pciDSSLevel.label}
                </option>
              ))}
            </select>
          </div>
          <div className="pcidss-stats">
            <dl className="mb-0">
              <div className="pcidss-stats__item py-2 d-flex align-items-center justify-content-between">
                <dt className="pcidss-stats__term">Transaction Count Limit</dt>
                <dd className="pcidss-stats__value mb-0">
                  {formatWithCommas(
                    isPreviousLevel
                      ? defaultLimits?.customer.pcidss_level?.[newLevel].yearly_transaction_count
                      : currentTransactionCountLimit
                  )}
                </dd>
              </div>

              <div className="pcidss-stats__item py-2 d-flex align-items-center justify-content-between">
                <dt className="pcidss-stats__term">Number of Issuable Cards</dt>
                <dd className="pcidss-stats__value mb-0">
                  {formatWithCommas(
                    isPreviousLevel ? defaultLimits?.customer.pcidss_level?.[newLevel].yearly_issued_cards : currentCardLimit
                  )}
                </dd>
              </div>
            </dl>
          </div>
        </div>
      ),
      size: 'md',
      secondButtonText: 'Update'
    },
    confirm_level_update: {
      size: 'sm',
      equalFooterBtn: true,
      secondaryCompletedModal: true,
      heading: 'Confirm PCI DSS Level Update',
      description: 'Kindly confirm that you want to update this merchant’s PCI DSS level. This action will take effect immediately.',
      completedDescription: "The merchant's PCI DSS level has been successfully updated and saved.",
      secondButtonText: 'Yes, Confirm',
      firstButtonText: 'Back',
      firstButtonAction: () => setStep('level_update'),
      content: (
        <div className="form-group modal-content">
          <div className="info">
            <Icon name="infoSolid" fill="#2376F3" />
            <span style={{ color: '#2376F3' }}>
              Any changes to this configuration may impact the merchant's compliance requirements and operational limits.
            </span>
          </div>
          <div className="mt-3">
            <div className="checkbox-container align-items-start">
              <input
                type="checkbox"
                onChange={e => setUpdateIsConfirmed(e.target.checked)}
                className="mt-1"
                id="confirmAction"
                name="confirmAction"
              />
              <div>
                <label className="mt-0" htmlFor="confirmAction">
                  Yes, I understand the implications of this action
                </label>
              </div>
            </div>
          </div>
        </div>
      )
    }
  };

  return <Modal close={onClose} {...modalStepProps[step]} secondButtonDisable={disableSubmit} secondButtonAction={handleSubmit} />;
}
