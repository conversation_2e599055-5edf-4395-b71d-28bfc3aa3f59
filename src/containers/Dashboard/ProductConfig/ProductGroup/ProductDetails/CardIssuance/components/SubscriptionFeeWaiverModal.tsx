import React, { useState } from 'react';
import { useMutation } from 'react-query';

import Icon from '+containers/Dashboard/Shared/Icons';
import Modal from '+containers/Dashboard/Shared/Modal';
import { useFeedbackHandler } from '+hooks';
import APIRequest from '+services/api-services';
import { CurrencyType, VirtualCardCategory } from '+types';
import { logError } from '+utils';

type ModalStepType = 'set_waiver' | 'confirm_waiver';

const api = new APIRequest();

export default function SubscriptionFeeWaiverModal({
  onClose,
  type,
  cardCategory,
  feeIsActive,
  merchantDetails,
  onToggleSuccess,
  currency
}: {
  currency: CurrencyType;
  merchantDetails?: {
    name: string;
    reference: string;
  };
  onClose: () => void;
  type: 'global' | 'merchant';
  cardCategory: VirtualCardCategory;
  feeIsActive: boolean;
  onToggleSuccess: () => void;
}) {
  const { feedbackInit, closeFeedback } = useFeedbackHandler();
  const [step, setStep] = useState<ModalStepType>('set_waiver');
  const [isWaived, setIsWaived] = useState(!feeIsActive);
  const isMerchantContext = type === 'merchant';
  const isCustomerCardCategory = cardCategory === 'customer';
  const cardCategoryText = isCustomerCardCategory ? 'Customer' : 'Reserved';
  const merchantTargetText = isMerchantContext ? merchantDetails?.name : 'merchants';
  const merchantSubjectText = isMerchantContext ? 'This merchant' : 'Merchants';
  const waiverActionVerbPresent = isWaived ? 'Waive' : 'Apply';
  const waiverActionVerbGerund = isWaived ? 'waiving' : 'applying';
  const waiverActionVerbPast = isWaived ? 'waived' : 'applied';
  const waiveFeeForMerchant = useMutation(
    ({ reference, enable }: { reference?: string; enable: boolean }) =>
      api.waiveSubcriptionFeeForMerchant({
        cardCategory: isCustomerCardCategory ? 'customer' : 'reserved',
        reference,
        enable,
        currency
      }),
    {
      onSuccess: () => onToggleSuccess(),
      onError: error => {
        logError(error);
        feedbackInit({
          message: error.response?.data?.message || 'Action was unsuccessful',
          type: 'danger',
          componentLevel: true
        });
      },
      onSettled: () => {
        setTimeout(() => {
          closeFeedback();
        }, 5000);
      }
    }
  );

  const handleSubmit = async () => {
    if (step === 'set_waiver') setStep('confirm_waiver');
    else if (step === 'confirm_waiver' && isMerchantContext)
      await waiveFeeForMerchant.mutateAsync({
        reference: merchantDetails?.reference,
        enable: !isWaived
      });
    else if (step === 'confirm_waiver' && !isMerchantContext)
      await waiveFeeForMerchant.mutateAsync({
        enable: !isWaived
      });
    else onClose();
  };

  const gotoPreviousStep = () => {
    if (step === 'confirm_waiver') setStep('set_waiver');
    else onClose();
  };

  const modalStepProps: Record<ModalStepType, Partial<React.ComponentProps<typeof Modal>>> = {
    set_waiver: {
      heading: 'Customize Subscription Fee',
      description: `Decide whether to charge or waive ${cardCategoryText} Cards subscription fee for ${merchantTargetText}.`,
      headerBottomBorder: true,
      content: (
        <div className="p-2">
          <div className="d-flex align-items-start" style={{ gap: '0.5rem', paddingBottom: '1rem' }}>
            <input type="checkbox" className="mt-1" checked={isWaived} onChange={e => setIsWaived(e.target.checked)} id="waiverApplied" />
            <div>
              <label htmlFor="waiverApplied" className="mt-0">
                Waive subscription fee
              </label>
              <p
                className="mb-0"
                style={{
                  color: '#414F5F',
                  opacity: 0.7,
                  fontSize: '0.8rem'
                }}
              >
                {merchantSubjectText} will not be billed a subscription fee once this is waived.
              </p>
            </div>
          </div>
        </div>
      ),
      size: 'md',
      secondButtonActionIsTerminal: false,
      secondButtonDisable: isWaived !== feeIsActive
    },
    confirm_waiver: {
      secondaryCompletedModal: true,
      heading: `${waiverActionVerbPresent} Subscription Fee?`,
      description: `Kindly confirm that you want to proceed with ${waiverActionVerbGerund} ${cardCategoryText} Cards subscription fee for ${merchantTargetText}.`,
      secondButtonText: isWaived ? 'Yes, Waive' : 'Yes, Apply',
      firstButtonText: 'Back',
      firstButtonAction: () => setStep('set_waiver'),
      completedDescription: `You have successfully ${waiverActionVerbPast} ${cardCategoryText} Cards  subscription fee for ${merchantTargetText}.`,
      size: 'sm',
      equalFooterBtn: true,
      content: (
        <div className="px-2 d-flex">
          <Icon name="infoSolid" height={16} width={16} className="mt-1 flex-shrink-0" fill="#2376F3" />
          <span className="font-weight-bold" style={{ color: '#2376F3' }}>
            Note that {merchantSubjectText.toLowerCase()} will {isWaived ? 'not' : ''} be charged a subscription fee to access{' '}
            {cardCategoryText} Cards features.
          </span>
        </div>
      )
    }
  };

  return <Modal {...modalStepProps[step]} secondButtonAction={handleSubmit} firstButtonAction={gotoPreviousStep} close={onClose} />;
}
