import { screen, waitFor, within } from '@testing-library/react';
import { http, HttpResponse } from 'msw';

import { mockIssuingMerchantDetails } from '+mock/mockData';
import { server } from '+mock/mockServers';

import { renderApprovedMerchantDetails } from './common';

beforeAll(() => {
  mockIssuingMerchantDetails.data.config.customer.status = 'active';
});

afterAll(() => {
  mockIssuingMerchantDetails.data.config.customer.status = 'inactive';
});

const updatePciLevelHandlerError = http.patch('/admin/merchants/card-issuance/KPY12345/pci-dss', () => {
  return HttpResponse.json({ message: 'An unexpected error occurred.' }, { status: 500 });
});

describe('Update Merchant PCI DSS level', () => {
  test('should render the modal with initial level and disabled update button', async () => {
    const { user } = await renderApprovedMerchantDetails();

    await user.click(await screen.findByRole('button', { name: /update/i }));

    const dialog = screen.getByRole('dialog', { hidden: true });

    expect(within(dialog).getByText(/update pci dss level/i)).toBeInTheDocument();
    expect((within(dialog).getByRole('option', { name: /level 1/i, hidden: true }) as HTMLOptionElement).selected).toBe(true);
    expect(within(dialog).getByRole('button', { name: /update/i, hidden: true })).toBeDisabled();
  });

  test('should display correct limits for the selected level', async () => {
    const { user } = await renderApprovedMerchantDetails();

    // Open the modal
    await user.click(await screen.findByRole('button', { name: /update/i }));

    const dialog = screen.getByRole('dialog', { hidden: true });

    const initialTransactionLimitElement = within(dialog)
      .getByText(/transaction count limit/i)
      .closest('.pcidss-stats__item')
      ?.querySelector('.pcidss-stats__value');
    const initialCardLimitElement = within(dialog)
      .getByText(/number of issuable cards/i)
      .closest('.pcidss-stats__item')
      ?.querySelector('.pcidss-stats__value');

    expect(initialTransactionLimitElement).toBeInTheDocument();
    expect(initialCardLimitElement).toBeInTheDocument();

    const initialTransactionValue = initialTransactionLimitElement?.textContent || '';
    const initialCardValue = initialCardLimitElement?.textContent || '';

    await user.selectOptions(within(dialog).getByLabelText(/merchant\'s pci dss level/i), 'Level 2');

    await waitFor(() => {
      const updatedTransactionValue = within(dialog)
        .getByText(/transaction count limit/i)
        .closest('.pcidss-stats__item')
        ?.querySelector('.pcidss-stats__value')?.textContent;

      expect(updatedTransactionValue).not.toBe(initialTransactionValue);

      const updatedCardValue = within(dialog)
        .getByText(/number of issuable cards/i)
        .closest('.pcidss-stats__item')
        ?.querySelector('.pcidss-stats__value')?.textContent;

      expect(updatedCardValue).not.toBe(initialCardValue);
    });
  });

  test('should enable update button when a new level is selected', async () => {
    const { user } = await renderApprovedMerchantDetails();
    await user.click(await screen.findByRole('button', { name: /update/i, hidden: true }));

    const dialog = screen.getByRole('dialog', { hidden: true });
    expect(within(dialog).getByRole('button', { name: /update/i, hidden: true })).toBeDisabled();

    await user.selectOptions(within(dialog).getByLabelText(/merchant\'s pci dss level/i), 'Level 2');

    expect(within(dialog).getByRole('button', { name: /update/i, hidden: true })).not.toBeDisabled();

    await user.selectOptions(within(dialog).getByLabelText(/merchant\'s pci dss level/i), 'Level 1');

    expect(within(dialog).getByRole('button', { name: /update/i, hidden: true })).toBeDisabled();
  });

  test('should proceed to confirmation step and successfully submit the update', async () => {
    const { user } = await renderApprovedMerchantDetails();
    await user.click(await screen.findByRole('button', { name: /update/i }));

    const dialog = screen.getByRole('dialog', { hidden: true });

    await user.selectOptions(within(dialog).getByLabelText(/merchant\'s pci dss level/i), 'Level 2');

    expect(within(dialog).getByRole('button', { name: /update/i, hidden: true })).not.toBeDisabled();

    await user.click(within(dialog).getByRole('button', { name: /update/i, hidden: true }));

    await waitFor(() => {
      expect(within(dialog).getByText(/confirm pci dss level update/i)).toBeInTheDocument();
    });

    await user.click(within(dialog).getByRole('checkbox', { hidden: true }));

    await user.click(within(dialog).getByRole('button', { name: /yes, confirm/i, hidden: true }));

    await waitFor(() => {
      expect(screen.getByText(/the merchant\'s pci dss level has been successfully updated and saved./i)).toBeInTheDocument();
    });

    await user.click(within(dialog).getByRole('button', { name: /dismiss/i, hidden: true }));

    await waitFor(() => {
      expect(screen.queryByRole('dialog', { hidden: true })).not.toBeInTheDocument();
    });
  });

  test('should show an error message if the API call fails', async () => {
    server.use(updatePciLevelHandlerError);

    const { user } = await renderApprovedMerchantDetails();
    await user.click(await screen.findByRole('button', { name: /update/i }));

    const dialog = screen.getByRole('dialog', { hidden: true });

    await user.selectOptions(within(dialog).getByLabelText(/merchant's pci dss level/i), 'Level 3');
    await user.click(within(dialog).getByRole('button', { name: /update/i, hidden: true }));
    await user.click(within(dialog).getByRole('checkbox', { hidden: true }));
    await user.click(within(dialog).getByRole('button', { name: /yes, confirm/i, hidden: true }));

    await waitFor(() => {
      expect(screen.getByText('An unexpected error occurred.')).toBeInTheDocument();
    });
  });

  test('should close the modal when the close button is clicked', async () => {
    const { user } = await renderApprovedMerchantDetails();
    await user.click(await screen.findByRole('button', { name: /update/i }));

    const dialog = screen.getByRole('dialog', { hidden: true });
    expect(dialog).toBeInTheDocument();

    const closeButton = within(dialog).getByTestId('close-button');
    await user.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByRole('dialog', { hidden: true })).not.toBeInTheDocument();
    });
  });
});
