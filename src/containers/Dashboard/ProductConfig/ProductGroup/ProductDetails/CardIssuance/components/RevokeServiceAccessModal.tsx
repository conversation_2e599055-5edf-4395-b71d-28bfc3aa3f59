import React, { useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { ErrorMessage, Field, Formik, useFormikContext } from 'formik';

import Icon from '+containers/Dashboard/Shared/Icons';
import Modal, { IModalProps } from '+containers/Dashboard/Shared/Modal';
import { useFeedbackHandler } from '+hooks';
import APIRequest from '+services/api-services';
import { CategoryType, CurrencyType, ModifyAccessPayloadType } from '+types';
import { advanceCleanInput, logError } from '+utils';

type FormValueType = {
  declineReason: string;
  otherDeclineReason: string;
  confirmAction: boolean;
};

type AccessToggleModalStepType = 'initial' | 'confirmation';

const apiRequest = new APIRequest();

const options = [
  { label: '--Select a reason--', value: '' },
  { label: 'Fraudulent activities', value: 'fraudulent_activities' },
  { label: 'Other', value: 'other' }
];

const RevokeServiceAccessModal = ({
  onClose,
  merchantKoraId,
  currency,
  feature
}: {
  onClose: () => void;
  currency: CurrencyType;
  merchantKoraId?: number;
  feature: CategoryType;
}) => {
  const queryClient = useQueryClient();
  const [step, setStep] = useState<AccessToggleModalStepType>('initial');
  const { feedbackInit, closeFeedback } = useFeedbackHandler();
  const cardCategory = feature === 'issued-cards' ? 'issued' : 'reserved';

  const validateForm = (values: FormValueType) => {
    let errors: Record<string, string> = {};
    if (step === 'initial') {
      if (!values.declineReason) {
        errors.declineReason = 'A reason is required';
      }
      if (values.declineReason === 'other' && !values.otherDeclineReason) {
        errors.otherDeclineReason = 'Please specify the reason';
      }
    } else if (step === 'confirmation') {
      if (String(values.confirmAction) !== 'true') {
        errors.confirmAction = 'You must confirm the action';
      }
    }
    return errors;
  };

  const { mutateAsync: mutateModifyAccess } = useMutation((payload: ModifyAccessPayloadType) => apiRequest.modifyAccess(payload), {
    onSuccess: data => {
      feedbackInit({ message: data?.message, type: 'success' });
      queryClient.invalidateQueries([`${currency}_ALL_PRODUCT_CONFIG_SETTING`]);
      queryClient.invalidateQueries(['REQUESTING_MERCHANT_DETAILS']);
      queryClient.invalidateQueries(['ISSUING_MERCHANT_PLANS']);
      queryClient.invalidateQueries(['MERCHANT_DETAILS']);
      queryClient.invalidateQueries(['ISSUING_MERCHANTS']);
      queryClient.invalidateQueries(['REQUESTING_MERCHANTS']);
    },
    onError: error => {
      logError(error);
      feedbackInit({
        message: error.response?.data?.message || 'Action was unsuccessful',
        type: 'danger',
        componentLevel: true
      });
    },
    onSettled: () => {
      setTimeout(() => {
        closeFeedback();
      }, 5000);
    }
  });

  const currentModalProps: Record<AccessToggleModalStepType, Partial<IModalProps>> = {
    initial: {
      heading: 'Disable access for Merchant',
      description: `Provide the details below to disable merchant access to USD ${cardCategory} cards.`,
      secondButtonText: 'Next',
      content: <AccessToggleForm />,
      secondButtonActionIsTerminal: false,
      secondButtonColor: '#F32345',
      headerBottomBorder: true,
      size: 'md'
    },
    confirmation: {
      size: 'sm',
      heading: 'Disable issued cards access for this merchant?',
      description: 'Please confirm that you want to disable card issuance access for this merchant. ',
      completedHeading: 'Disabled',
      completedDescription: `You have disabled this merchant’s access to ${cardCategory} cards.`,
      secondButtonText: 'Yes, Confirm',
      secondButtonActionIsTerminal: true,
      firstButtonAction: () => setStep('initial'),
      firstButtonText: 'Back',
      equalFooterBtn: true,
      secondButtonColor: '#F32345',
      content: <AccessToggleConfirmation cardCategory="issued" />
    }
  };

  return (
    <Formik
      validate={validateForm}
      initialValues={{
        declineReason: '',
        otherDeclineReason: '',
        confirmAction: false
      }}
      onSubmit={async formValues => {
        try {
          switch (step) {
            case 'initial':
              setStep('confirmation');
              break;
            case 'confirmation':
              await mutateModifyAccess({
                currency,
                kora_id: Number(merchantKoraId),
                action: 'disable',
                card_type: 'virtual',
                access_type: 'service',
                reason: advanceCleanInput(formValues.otherDeclineReason || formValues.declineReason)
              });
              break;
            default:
              break;
          }
        } catch (error) {
          throw error;
        }
      }}
    >
      {props => {
        return (
          <Modal
            close={onClose}
            {...currentModalProps[step]}
            secondaryCompletedModal
            secondButtonAction={props.submitForm}
            secondButtonDisable={step === 'confirmation' && !props.values.confirmAction}
          />
        );
      }}
    </Formik>
  );
};

export default RevokeServiceAccessModal;

const AccessToggleConfirmation = ({ cardCategory }: { cardCategory: string }) => {
  return (
    <div className="form-group modal-content">
      <div className="info">
        <Icon name="infoSolid" fill="#F32345" />
        <span style={{ color: '#F32345' }}>
          Important: This means that this merchant won’t be able to issue any new cards and their {cardCategory} virtual cards will be
          suspended.
        </span>
      </div>
      <div className="mt-3">
        <div className="checkbox-container align-items-start">
          <Field as="input" type="checkbox" className="mt-1" id="confirmAction" name="confirmAction" />
          <div>
            <label className="mt-0" htmlFor="confirmAction">
              Yes, I understand the implications of this action
            </label>
            <ErrorMessage name="confirmAction" component="div" className="form-error mt-1" />
          </div>
        </div>
      </div>
    </div>
  );
};

const AccessToggleForm = () => {
  const { values } = useFormikContext<FormValueType>();
  return (
    <div className="modal-content">
      <div className="">
        <label htmlFor="declineReason"> Reason </label>
        <Field as="select" className="form-control" name="declineReason">
          {options.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Field>
        <ErrorMessage name="declineReason" component="div" className="form-error mt-1" />
      </div>
      {values.declineReason === 'other' && (
        <div className="my-3">
          <label htmlFor="otherDeclineReason">Tell us why you want to disable access for this merchant</label>
          <Field
            as="textarea"
            id="otherDeclineReason"
            name="otherDeclineReason"
            rows={4}
            className="form-control"
            placeholder="Enter the specific reason..."
          />
          <ErrorMessage name="otherDeclineReason" component="div" className="form-error mt-1" />
        </div>
      )}
    </div>
  );
};
