import React from 'react';
import { ErrorMessage, Field } from 'formik';

import Icon from '+containers/Dashboard/Shared/Icons';

const List = ({ items }: { items: Array<{ label: string; value: string }> }) => {
  return (
    <ul className="requesting-merchant-overview__list">
      {items.map(item => (
        <li className="requesting-merchant-overview__list-item">
          <span>{item.label}</span>
          <span>{item.value}</span>
        </li>
      ))}
    </ul>
  );
};

export const RequestingMerchantOverview = ({
  requestDetails,
  spendingLimitDetails,
  fundingLimitDetails,
  pcidssLimitDetails
}: Record<'requestDetails' | `${'spending' | 'funding' | 'pcidss'}LimitDetails`, Array<{ label: string; value: string }>>) => {
  return (
    <div className="requesting-merchant-overview">
      <section>
        <h3 className="requesting-merchant-overview__heading1">Merchant request detail</h3>
        <List items={requestDetails} />
      </section>
      <section>
        <h3 className="requesting-merchant-overview__heading1">Limits based on risk level</h3>
        <section>
          <h4 className="requesting-merchant-overview__heading2">Spending limits</h4>
          <List items={spendingLimitDetails} />
        </section>
        <section>
          <h4 className="requesting-merchant-overview__heading2">Funding limits</h4>
          <List items={fundingLimitDetails} />
        </section>
      </section>
      <section>
        <h3 className="requesting-merchant-overview__heading1">Limits based on PCI DSS level</h3>
        <List items={pcidssLimitDetails} />
      </section>
    </div>
  );
};

export const ConfirmRequestApproval = () => {
  return (
    <div className="form-group modal-content">
      <div className="info">
        <Icon name="infoSolid" fill="#2376F3" />
        <span style={{ color: '#2376F3' }}>
          Important: This means that this merchant will be able to issue virtual cards under the default card issuance configuration to
          their customers.
        </span>
      </div>
      <div className="mt-3">
        <div className="checkbox-container align-items-start">
          <Field as="input" type="checkbox" className="mt-1" id="confirmAction" name="confirmAction" />
          <div>
            <label className="mt-0" htmlFor="confirmAction">
              Yes, I understand the implications of this action
            </label>
            <ErrorMessage name="confirmAction" component="div" className="form-error mt-1" />
          </div>
        </div>
      </div>
    </div>
  );
};
