import { screen, within } from '@testing-library/react';

import { mockIssuingMerchantDetails } from '+mock/mockData';

import { renderApprovedMerchantDetails } from './common';

describe('WalletFundingFeeWaiverSettings', () => {
  const originalMock = JSON.parse(JSON.stringify(mockIssuingMerchantDetails.data.config.issuing_wallet_funding_fee));

  test('renders WalletFundingFeeWaiverSettings with correct values and controls', async () => {
    await renderApprovedMerchantDetails();

    expect(await screen.findByText(/this is what we charge merchants whenever they fund their card issuing wallet/i)).toBeInTheDocument();
    expect(screen.getByText(/wallet funding transaction fee/i)).toBeInTheDocument();
    expect(screen.getByText('2.5%')).toBeInTheDocument();
  });

  test('shows loading placeholder when walletFundingFee is not present', async () => {
    mockIssuingMerchantDetails.data.config.issuing_wallet_funding_fee = undefined;
    renderApprovedMerchantDetails();
    expect(await screen.findByTestId(/loading-placeholder/i)).toBeInTheDocument();
    mockIssuingMerchantDetails.data.config.issuing_wallet_funding_fee = originalMock;
  });

  test('enabling fee waiver disables fee amount and fee type updation', async () => {
    const { user } = await renderApprovedMerchantDetails();
    await user.click(await screen.findByRole('button', { name: /edit issuing wallet funding fee/i }));
    const modal = within(await screen.findByRole('dialog', { hidden: true }));

    const waiverToggle = modal.getByRole('checkbox', { name: /waive wallet funding fee/i, hidden: true });
    const feeTypeSelector = modal.getByLabelText(/fee Type/i);
    const amountInput = modal.getByLabelText(/amount/i);

    expect(await modal.findByText(/customize issuing wallet funding fee/i)).toBeInTheDocument();
    expect(waiverToggle).not.toBeChecked();
    expect(feeTypeSelector).toHaveValue('percentage');
    expect(amountInput).toHaveValue('2.50');

    await user.click(waiverToggle);
    expect(waiverToggle).toBeChecked();
    await user.selectOptions(feeTypeSelector, 'flat');
    expect(modal.getByText('%')).toBeInTheDocument();
    expect((modal.getByRole('option', { name: /flat/i, hidden: true }) as HTMLOptionElement).selected).toBe(false);
    await user.type(amountInput, '100');
    expect(amountInput).toHaveValue('2.50');
  });

  test('disabling fee waiver enables fee amount and fee type updation', async () => {
    const { user } = await renderApprovedMerchantDetails();
    await user.click(await screen.findByRole('button', { name: /edit issuing wallet funding fee/i }));

    const modal = within(await screen.findByRole('dialog', { hidden: true }));
    const waiverToggle = modal.getByRole('checkbox', { name: /waive wallet funding fee/i, hidden: true });
    const feeTypeSelector = modal.getByLabelText(/fee Type/i);
    const amountInput = modal.getByLabelText(/amount/i);
    const continueBtn = modal.getByRole('button', { name: /continue/i, hidden: true });

    expect(await modal.findByText(/customize issuing wallet funding fee/i)).toBeInTheDocument();
    expect(waiverToggle).not.toBeChecked();
    expect(feeTypeSelector).toHaveValue('percentage');
    expect(amountInput).toHaveValue('2.50');

    await user.selectOptions(feeTypeSelector, 'flat');
    expect((modal.getByRole('option', { name: /flat/i, hidden: true }) as HTMLOptionElement).selected).toBe(true);
    expect(modal.getByText('USD')).toBeInTheDocument();

    await user.clear(amountInput);
    await user.type(amountInput, '100');
    expect(amountInput).toHaveValue('100.00');

    expect(continueBtn).not.toBeDisabled();
    await user.click(modal.getByRole('button', { name: /continue/i, hidden: true }));

    expect(await modal.findByText(/confirm issuing wallet funding fee update/i)).toBeInTheDocument();
    expect(
      await modal.findByText(/kindly confirm that you want to apply the modification\(s\) to this configuration/i)
    ).toBeInTheDocument();
    expect(
      await modal.findByText(
        /note that this merchant will be charged 100\.00 undefined of their funding amount every time they fund their card issuing wallet/i
      )
    ).toBeInTheDocument();

    await user.click(modal.getByRole('button', { name: /yes, confirm/i, hidden: true }));
    expect(modal.getByText(/you have successfully made changes to this configuration/i)).toBeInTheDocument();
  });
});
