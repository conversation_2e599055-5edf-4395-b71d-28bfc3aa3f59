import useProductConfigCardIssuanceStore from '+store/productConfigCardIssuanceStore';
import { AccessRequestMerchantResponse, CurrencyType } from '+types';
import { formatAmount, formatWithCommas } from '+utils';

import { monthlyPaymentValuesMap, pciDSSLevelMap, riskLevelMap } from './approvedMerchantDetailsHelpers';

export const generateMerchantRequestDetailsFrom = (data?: AccessRequestMerchantResponse) => {
  if (!data) return [];
  return [
    {
      label: 'Merchant name',
      value: data.name
    },
    {
      label: 'Risk level',
      value: riskLevelMap[data.risk_level]
    },
    {
      label: 'PCI DSS level',
      value: pciDSSLevelMap[data.pci_dss_level]
    },
    {
      label: 'Preferred plan',
      value: `${data.subscription_plan} Plan`
    },
    {
      label: 'Monthly payment value',
      value: monthlyPaymentValuesMap[data.monthly_payment_value]
    }
  ];
};

export const generateMerchantRiskLevelSpendingLimitDetailsFrom = (currency: CurrencyType, data?: AccessRequestMerchantResponse) => {
  if (!data) return [];
  const productConfigStore = useProductConfigCardIssuanceStore.getState();
  const merchantSpendingLimits = productConfigStore.defaultConfig.customer.risk_level?.[data.risk_level].spending_limit;

  return [
    {
      label: `Maximum limit per transaction (${currency})`,
      value: formatAmount(merchantSpendingLimits?.per_transaction_max)
    },
    {
      label: `Daily transaction cap (${currency})`,
      value: formatAmount(merchantSpendingLimits?.daily_max)
    },
    {
      label: `Monthly transaction cap (${currency})`,
      value: formatAmount(merchantSpendingLimits?.monthly_max)
    }
  ];
};

export const generateMerchantRiskLevelFundingLimitDetailsFrom = (currency: CurrencyType, data?: AccessRequestMerchantResponse) => {
  if (!data) return [];
  const productConfigStore = useProductConfigCardIssuanceStore.getState();
  const merchantFundingLimits = productConfigStore.defaultConfig.customer.risk_level?.[data.risk_level].funding_limit;
  return [
    {
      label: `Maximum daily funding (${currency})`,
      value: formatAmount(merchantFundingLimits?.daily_max)
    },
    {
      label: `Maximum monthly funding (${currency})`,
      value: formatAmount(merchantFundingLimits?.monthly_max)
    },
    {
      label: `Maximum quarterly funding (${currency})`,
      value: formatAmount(merchantFundingLimits?.quarterly_max)
    }
  ];
};

export const generateMerchantPCIDSSLimitDetailsFrom = (data?: AccessRequestMerchantResponse) => {
  if (!data) return [];

  const productConfigStore = useProductConfigCardIssuanceStore.getState();
  const merchantPCIDSSLimits = productConfigStore.defaultConfig.customer.pcidss_level?.[data.pci_dss_level];

  return [
    {
      label: 'PCI DSS certification level',
      value: pciDSSLevelMap[data?.pci_dss_level]
    },
    {
      label: 'Transaction count',
      value: formatWithCommas(merchantPCIDSSLimits?.yearly_transaction_count)
    },
    {
      label: 'Number of issuable cards',
      value: formatWithCommas(merchantPCIDSSLimits?.yearly_issued_cards)
    }
  ];
};

export const declineReasonOptions = [
  { label: '--Select a reason--', value: '' },
  { label: 'PCI DSS Non-Compliance', value: 'PCI DSS Non-Compliance' },
  { label: 'Risk or Fraud Concerns ', value: 'Risk or Fraud Concerns ' },
  { label: 'Internal Business Decision', value: 'Internal Business Decision' },
  { label: 'Others', value: 'others' }
];
