import React from 'react';

import RiskIndicator from '+dashboard/ProductConfig/components/RiskIndicator';
import {
  CardIssuanceDefaultLimitsResponseType,
  IssuingMerchantDetailsResponseType,
  MerchantFundingLimitsType,
  MerchantPCIDSSLimitsType,
  MerchantSpendingLimitsType,
  PCIDSSLevelType,
  RiskLevelType
} from '+types';
import { formatAmount, formatWithCommas, getDateAndTime } from '+utils';

export const riskLevelMap = {
  low_risk: 'Low Risk',
  medium_risk: 'Medium Risk',
  above_average_risk: 'Semi-high Risk',
  high_risk: 'High Risk'
} as const;

export const pciDSSLevelMap = {
  level_4: 'Level 4',
  level_3: 'Level 3',
  level_2: 'Level 2',
  level_1: 'Level 1',
  level_0: 'Level 0'
} as const;

export const formatPCIDSSLimits = (data: CardIssuanceDefaultLimitsResponseType['customer']['pcidss_level']) => {
  if (!data) return { levels: [], yearlyTransactionCount: [], yearlyIssuedCards: [] };

  return Object.keys(data).reduce<Record<'levels' | 'yearlyTransactionCount' | 'yearlyIssuedCards', Array<React.ReactNode>>>(
    (accum, next) => ({
      ...accum,
      levels: [...accum.levels, next.replace('level_', 'Level ')],
      yearlyTransactionCount: [...accum.yearlyTransactionCount, formatWithCommas(data[next as PCIDSSLevelType].yearly_transaction_count)],
      yearlyIssuedCards: [...accum.yearlyIssuedCards, formatWithCommas(data[next as PCIDSSLevelType].yearly_issued_cards)]
    }),
    { levels: [], yearlyTransactionCount: [], yearlyIssuedCards: [] }
  );
};

export const formatFundingLimits = (data: CardIssuanceDefaultLimitsResponseType['customer']['risk_level']) => {
  if (!data) return { dailyLimit: [], monthlyLimit: [], quarterlyLimit: [] };

  const desiredKeyOrder: Array<string> = ['low_risk', 'medium_risk', 'above_average_risk', 'high_risk'];

  const allKeysPresentInData = Object.keys(data);
  const sortedKeysToProcess = desiredKeyOrder.filter(key => allKeysPresentInData.includes(key));

  return sortedKeysToProcess.reduce<Record<'dailyLimit' | 'monthlyLimit' | 'quarterlyLimit', Array<React.ReactNode>>>(
    (accum, next) => ({
      ...accum,
      dailyLimit: [...accum.dailyLimit, `$${formatAmount(data[next as RiskLevelType].funding_limit.daily_max)}`],
      monthlyLimit: [...accum.monthlyLimit, `$${formatAmount(data[next as RiskLevelType].funding_limit.monthly_max)}`],
      quarterlyLimit: [...accum.quarterlyLimit, `$${formatAmount(data[next as RiskLevelType].funding_limit.quarterly_max)}`]
    }),
    { dailyLimit: [], monthlyLimit: [], quarterlyLimit: [] }
  );
};

export const formatSpendingLimits = (data: CardIssuanceDefaultLimitsResponseType['customer']['risk_level']) => {
  if (!data) return { perTransactionMax: [], dailyMax: [], monthlyMax: [] };

  const desiredKeyOrder: Array<string> = ['low_risk', 'medium_risk', 'above_average_risk', 'high_risk'];

  const allKeysPresentInData = Object.keys(data);
  const sortedKeysToProcess = desiredKeyOrder.filter(key => allKeysPresentInData.includes(key));

  return sortedKeysToProcess.reduce<Record<'perTransactionMax' | 'dailyMax' | 'monthlyMax', Array<React.ReactNode>>>(
    (accum, next) => ({
      ...accum,
      perTransactionMax: [...accum.perTransactionMax, `$${formatAmount(data[next as RiskLevelType].spending_limit.per_transaction_max)}`],
      dailyMax: [...accum.dailyMax, `$${formatAmount(data[next as RiskLevelType].spending_limit.daily_max)}`],
      monthlyMax: [...accum.monthlyMax, `$${formatAmount(data[next as RiskLevelType].spending_limit.monthly_max)}`]
    }),
    { perTransactionMax: [], dailyMax: [], monthlyMax: [] }
  );
};

export const formatMerchantPCIDSSLimits = (data: MerchantPCIDSSLimitsType) => {
  if (!data) return {};
  return {
    yearlyTransactionCount: formatWithCommas(data.yearly_transaction_count),
    yearlyIssuedCards: formatWithCommas(data.yearly_issued_cards)
  };
};

export const formatMerchantFundingLimits = (data: MerchantFundingLimitsType) => {
  if (!data) return {};
  return {
    dailyLimit: `$${formatAmount(data.daily_max)}`,
    monthlyLimit: `$${formatAmount(data.monthly_max)}`,
    quarterlyLimit: `$${formatAmount(data.quarterly_max)}`
  };
};

export const formatMerchantSpendingLimits = (data: MerchantSpendingLimitsType) => {
  if (!data) return {};
  return {
    perTransactionMax: `$${formatAmount(data.per_transaction_max)}`,
    dailyMax: `$${formatAmount(data.daily_max)}`,
    monthlyMax: `$${formatAmount(data.monthly_max)}`
  };
};

export const formatSummary = (data: IssuingMerchantDetailsResponseType) => {
  if (!data) return [];
  return [
    { label: 'Available Card Issuing Balance', value: formatAmount(data.wallet_balance) + ' USD' },
    { label: 'USD Transaction Count', value: data.transactions_count },
    { label: 'Risk Rating', value: <RiskIndicator riskLevel={data.risk_level} /> },
    { label: 'Access Requested On', value: getDateAndTime(data.date_created) }
  ];
};

export const customerCardMonthlyPaymentOptions = [
  { value: 'LessThan50K', label: '$50,000 and Below' },
  { value: 'From50KTo100K', label: 'Between $50,000 - $100,000' },
  { value: 'From100KTo500K', label: 'Between $100,000 - $500,000' },
  { value: 'From500KTo1M', label: 'Between $500,000 - $1,000,000' },
  { value: 'Above1M', label: 'Above $1,000,000' }
] as const;

export const spendingLimitMap = {
  per_transaction_max: 'Maximum limit per transaction (USD)',
  daily_max: 'Daily transaction cap (USD)',
  monthly_max: 'Monthly transaction cap (USD)'
};

export const fundingLimitMap = {
  daily_max: 'Maximum daily funding (USD)',
  monthly_max: 'Maximum monthly funding (USD)',
  quarterly_max: 'Maximum quarterly funding (USD)'
};

export const pcidsssMap = {
  yearly_transaction_count: 'Transaction Count',
  yearly_issued_cards: 'Number of Issuable Cards'
};

export const monthlyPaymentValuesMap: Record<string, string> = customerCardMonthlyPaymentOptions.reduce<Record<string, string>>(
  (accumulator, currentItem) => {
    accumulator[currentItem.value] = currentItem.label;
    return accumulator;
  },
  {}
);
