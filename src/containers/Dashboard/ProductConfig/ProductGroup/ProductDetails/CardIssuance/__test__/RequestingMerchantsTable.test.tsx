import React from 'react';
import { render } from '@testing-library/react';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';
import { CategoryType } from '+types/productConfig';

import RequestingMerchantsTable from '../components/RequestingMerchantsTable';

const MockedRequestingMerchantsTable = ({ feature }: { feature: CategoryType }) => {
  return (
    <MockIndex>
      <RequestingMerchantsTable feature={feature} />
    </MockIndex>
  );
};

describe('RequestersTable', () => {
  it('RequestersTable is accessible', async () => {
    const { container } = render(<MockedRequestingMerchantsTable feature="issued-cards" />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
