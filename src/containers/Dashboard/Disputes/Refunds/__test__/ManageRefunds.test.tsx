import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import MockIndex from '+mock/MockIndex';

import ManageRefundsModal from '../ManageRefundsModal';

const MockedRefundsModal = () => {
  return (
    <MockIndex>
      <ManageRefundsModal visible close={vi.fn()} referenceId="KPY-RFD-d2mu4S16oS9j" currentStatus="manual" />
    </MockIndex>
  );
};

describe('Manage Refunds Modal', () => {
  test('renders correctly', () => {
    const { getByText } = render(<MockedRefundsModal />);
    expect(getByText('Manage Refund')).toBeInTheDocument();
  });

  test('Should show confirm after status selection', async () => {
    render(<MockedRefundsModal />);
    const statusChoices = screen.getAllByTestId('refund-type');
    await userEvent.click(statusChoices[0]);
    const confirmButton = screen.getByText('Proceed');
    await userEvent.click(confirmButton);
    const confirmHeading = screen.getByText('Update Status');
    expect(confirmHeading).toBeInTheDocument();
  });

  test('Should show reason for failure if failed status is selected', async () => {
    render(<MockedRefundsModal />);
    const statusChoices = screen.getAllByTestId('refund-type');
    await userEvent.click(statusChoices[1]); // Assuming the second option is 'failed'
    const statusReason = screen.getByTestId('status-reason');
    expect(statusReason).toBeInTheDocument();
  });

  test('Should show success when status is updated', async () => {
    render(<MockedRefundsModal />);
    const statusChoices = screen.getAllByTestId('refund-type');
    await userEvent.click(statusChoices[1]);
    const statusReason = screen.getByTestId('status-reason');
    await userEvent.selectOptions(statusReason, 'Name mismatch');
    const confirmButton = screen.getByText('Proceed');
    await userEvent.click(confirmButton);
    const proceedButton = screen.getByText('Yes, Proceed');
    await userEvent.click(proceedButton);
    const successHeading = screen.getByText('Status Updated!');
    expect(successHeading).toBeInTheDocument();
  });

  test('Should show textarea when reason for failure is others', async () => {
    render(<MockedRefundsModal />);
    const statusChoices = screen.getAllByTestId('refund-type');
    await userEvent.click(statusChoices[1]);
    const statusReason = screen.getByTestId('status-reason');
    await userEvent.selectOptions(statusReason, 'others');
    const textarea = screen.getByPlaceholderText('Enter reason for failure');
    expect(textarea).toBeInTheDocument();
    expect(screen.getByText('Proceed')).toBeDisabled();
    await userEvent.type(textarea, 'Some other reason');
    expect(screen.getByText('Proceed')).not.toBeDisabled();
  });
});
