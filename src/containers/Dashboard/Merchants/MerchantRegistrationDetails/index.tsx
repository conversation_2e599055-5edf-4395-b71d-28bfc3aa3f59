import React, { useState } from 'react';
import { useQuery } from 'react-query';

import Table from '+containers/Dashboard/Shared/Table';
import { useSetUserAccess } from '+hooks';
import useFeedbackHandler from '+hooks/useFeedbackHandler';
import useSearchQuery from '+hooks/useSearchQuery';
import APIRequest from '+services/api-services';
import { FileFormatType, MerchantRegistrationType } from '+types';
import { APIDownload, filteredOutObjectProperty, getDate, isAllowed, logError, queriesParams } from '+utils';

import { allRegistrationTableContent } from './merchantRegistrationData';
import RegistrationDetailsModal from './RegistrationDetailsModal';

import './index.scss';

const api = new APIRequest();

function MerchantRegistrationDetails() {
  const { feedbackInit } = useFeedbackHandler();
  const searchQuery = useSearchQuery();
  const page = Number(searchQuery.value.page) || 1;
  const limit = Number(searchQuery.value.limit) || 10;
  const [showModal, setShowModal] = useState(false);
  const [merchantDetails, setMerchantDetails] = useState<MerchantRegistrationType | null>(null);

  const valuesToBeRemoved = [queriesParams.tab, queriesParams.page, queriesParams.limit];
  const sortingParams = {
    ...filteredOutObjectProperty(searchQuery.value, valuesToBeRemoved)
  };

  const userAccess = useSetUserAccess();

  const { data: merchants, isFetching } = useQuery(
    ['MERCHANTS_REGISTRATION_DETAILS', page, sortingParams, limit],
    () => api.getMerchantsRegistrationDetails({ page, ...sortingParams, limit }),
    {
      refetchOnMount: true,
      onError: error => {
        logError(error);
        feedbackInit({
          message: 'There has been an error fetching all merchants, Please try again',
          type: 'danger'
        });
      }
    }
  );

  const showRegistrationDetails = (merchant: MerchantRegistrationType) => {
    setShowModal(true);
    setMerchantDetails(merchant);
  };
  const registrations = allRegistrationTableContent({ data: merchants?.data || [] }, showRegistrationDetails);

  const exportMerchantList = async (format: FileFormatType, close: () => void, fieldsToExport: string | string[]) => {
    try {
      const res = await api.exportRegister(fieldsToExport, format, sortingParams);
      const type = format || 'xlsx';
      APIDownload(res, `registrations list at ${getDate(Date.now())}`, type);
      feedbackInit({
        title: 'Export Successful',
        message: (
          <>
            - Successfully exported <strong>{merchants?.paging.total_items} records.</strong>
          </>
        ),
        type: 'success'
      });
      close();
    } catch (error) {
      logError(error);
      feedbackInit({
        title: 'Export Failed',
        message: `There has been an error downloading your preference list file`,
        type: 'danger',
        componentLevel: true
      });
    }
  };

  return (
    <div className="element-box merchant-registrations">
      <div className="element-box-heading">
        <div className="heading-box-mmd">
          <div className="payment-preferences-title">
            <h5 className="form-header" data-testid="page-title">
              Business Registration Details
            </h5>
          </div>
          <div className="form-desc no-underline mb-0" data-testid="page-subtitle">
            Here’s where you can view, manage, and edit merchant business registration.
          </div>
        </div>
      </div>

      <legend>
        <span />
      </legend>
      <div className="merchant-registrations__table-wrapper">
        <Table
          className={registrations.className}
          loading={isFetching}
          data={registrations?.data || []}
          renderFields
          hasPagination
          rowKey={registrations?.rowKey}
          tableHeadings={['Merchant', 'Location', 'Industry', 'Directors', 'Ultimate Beneficiary (UBO)', 'Shareholders']}
          current={page}
          totalItems={merchants?.paging.total_items}
          pageSize={merchants?.paging.page_size}
          tableWrapperClassName="table-responsive table-wrapper"
          annotation="Merchants"
          type="merchant-registrations"
          filterType="merchant-registrations"
          filterHasAdvancedFilter={false}
          filterKeywordPlaceholder="Search..."
          filterQueryIDPlaceholder="Merchant ID"
          filterExportAction={exportMerchantList}
          filterShowExport={isAllowed(userAccess, ['ubo_register.export']) as boolean}
          actionFn={(currentPage: string | number) => searchQuery.setQuery({ page: String(currentPage) })}
          limitAction={(currentLimit: number) => searchQuery.setQuery({ limit: String(currentLimit) })}
          emptyStateHeading={registrations?.emptyStateHeading || ''}
          emptyStateMessage={registrations.emptyStateMessage || ''}
          rowFn={registrations.rowFn}
        >
          {registrations?.fields}
        </Table>
      </div>

      {showModal && <RegistrationDetailsModal data={merchantDetails} close={() => setShowModal(false)} />}
    </div>
  );
}

export default MerchantRegistrationDetails;
