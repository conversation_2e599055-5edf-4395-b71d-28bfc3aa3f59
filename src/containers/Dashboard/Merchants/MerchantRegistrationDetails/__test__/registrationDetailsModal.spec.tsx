import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import MockIndex from '+mock/MockIndex';
import { MerchantRegistrationType } from '+types';

import RegistrationDetailsModal from '../RegistrationDetailsModal';

const mockClose = vi.fn();

const MockedRegistrationDetailsModals = () => {
  return (
    <MockIndex>
      <RegistrationDetailsModal close={mockClose} data={mockData} />
    </MockIndex>
  );
};

const mockData: MerchantRegistrationType = {
  id: 1,
  merchant_id: 234,
  merchant: {
    name: 'Test Merchant',
    status: 'active',
    location: 'Nigeria',
    email: '<EMAIL>',
    country_id: 2,
    created_at: '2025-04-24T11:14:12.000Z',
    industry: 'Retail',
    id: 234,
    tier_level: 'Base Tier',
    number_of_representatives: 6,
    number_of_directors: 2,
    number_of_shareholders: 0
  },
  created_at: '2025-05-01T12:00:00Z',
  updated_at: '2025-05-01T12:00:00Z',
  key_personnel: [
    { name: '<PERSON>', role: 'Manager', address: '1, Orchid <PERSON>' },
    { name: '<PERSON>', role: 'CEO', address: '2, Rose Avenue' }
  ]
};

describe('RegistrationDetailsModal', () => {
  it('renders the modal with merchant details', () => {
    render(<MockedRegistrationDetailsModals />);

    expect(screen.getByText(/Merchant Registration Details/)).toBeInTheDocument();
    expect(screen.getByText(/Test Merchant/)).toBeInTheDocument();
    expect(screen.getByText(/Nigeria/)).toBeInTheDocument();
    expect(screen.getByText(/<EMAIL>/)).toBeInTheDocument();
    expect(screen.getByText(/Retail/)).toBeInTheDocument();
    expect(screen.getByText(/234/)).toBeInTheDocument();
    expect(screen.getByText(/Base Tier/)).toBeInTheDocument();
    expect(screen.getByText(/6/)).toBeInTheDocument();
  });

  it('renders the business representatives section when key personnel data is available', () => {
    render(<MockedRegistrationDetailsModals />);

    expect(screen.getByText('Business Representatives')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
  });

  it('does not render the business representatives section when no key personnel data is available', () => {
    const dataWithoutKeyPersonnel = { ...mockData, key_personnel: [] };
    const MockedRegistrationDetails = () => {
      return (
        <MockIndex>
          <RegistrationDetailsModal close={mockClose} data={dataWithoutKeyPersonnel} />
        </MockIndex>
      );
    };
    render(<MockedRegistrationDetails />);

    expect(screen.queryByText('Business Representatives')).not.toBeInTheDocument();
  });

  it('renders "N/A" for missing merchant details', () => {
    const incompleteData = {
      ...mockData,
      merchant: { ...mockData.merchant, industry: '' }
    };

    const MockedRegistrationDetails = () => {
      return (
        <MockIndex>
          <RegistrationDetailsModal close={mockClose} data={incompleteData} />
        </MockIndex>
      );
    };
    render(<MockedRegistrationDetails />);

    expect(screen.getByText('N/A')).toBeInTheDocument();
  });

  it('calls the close function when the "Close" button is clicked', async () => {
    render(<MockedRegistrationDetailsModals />);

    const closeButton = screen.getByText('Close');
    await userEvent.click(closeButton);

    expect(mockClose).toHaveBeenCalledTimes(1);
  });

  it('renders the modal description', () => {
    render(<MockedRegistrationDetailsModals />);

    expect(
      screen.getByText(
        'Below, you will find detailed information about the merchant, including business registration and other relevant details.'
      )
    ).toBeInTheDocument();
  });
});
