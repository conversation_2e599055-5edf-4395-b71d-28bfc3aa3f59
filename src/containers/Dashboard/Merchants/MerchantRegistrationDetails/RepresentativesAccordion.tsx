import React, { ReactNode, useState } from 'react';

import { KeyPersonnelType } from '+types';
import { capitalize, capitalizeFirst, lowercaseRemovedash } from '+utils';

import './index.scss';

const DropdownContent = ({ label, value }: { label: string; value: ReactNode | undefined }) => (
  <div>
    <p>{label}</p>
    <p>{value}</p>
  </div>
);

const RepresentativesAccordion = ({ details, key }: { details: KeyPersonnelType; key: number }) => {
  const [toggle, setToggle] = useState(false);

  return (
    <div className="representative-dropdown" key={key}>
      <div className="dropdown-div" onClick={() => setToggle(!toggle)}>
        <h4>{capitalize(details?.name)}</h4>
        <p>{capitalize(lowercaseRemovedash(details?.role))}</p>
        <p>
          {details?.share_percentage ? details?.share_percentage + '%' : '--'}

          {toggle ? (
            <span>
              Less
              <i className="os-icon os-icon-chevron-up icon" />
            </span>
          ) : (
            <span>
              More
              <i className="os-icon os-icon-chevron-down icon" />
            </span>
          )}
        </p>
      </div>
      {toggle && (
        <div className="dropdown-content">
          <DropdownContent label="Address" value={capitalizeFirst(details?.address)} />

          {details.share_count && <DropdownContent label="Share Count" value={details.share_count} />}
          {details.share_value && <DropdownContent label="Share Value" value={details.share_value} />}
        </div>
      )}
    </div>
  );
};

export default RepresentativesAccordion;
