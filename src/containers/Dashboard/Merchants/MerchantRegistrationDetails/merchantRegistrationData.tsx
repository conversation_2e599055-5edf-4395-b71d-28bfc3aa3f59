import React from 'react';

import { MerchantRegistrationType } from '+types';
import { capitalize, capitalizeRemovedash, switchStatus } from '+utils';

const icon = {
  pending: { name: 'warningTriangle', size: 14, stroke: '' },
  rejected: { name: 'invalid', size: 12, stroke: 'red' },
  approved: {
    name: 'check',
    size: 10,
    stroke: 'green'
  }
} as const;

// eslint-disable-next-line import/prefer-default-export
export const allRegistrationTableContent = (
  { data }: { data: MerchantRegistrationType[] },
  action: (params: MerchantRegistrationType) => void
) => ({
  className: '--merchant-registrations-table',
  emptyStateHeading: 'No Business Registration Details',
  data,
  emptyStateMessage: `Merchant’s Business registration details will appear here.`,
  rowKey: 'merchant_id',
  rowFn: (each: MerchantRegistrationType) => action(each),
  fields: (each: MerchantRegistrationType) => {
    const directors =
      each?.key_personnel
        ?.filter(person => person.role === 'DIRECTOR')
        ?.map(person => person.name)
        ?.join(', ') || '-- --';

    const shareholders =
      each?.key_personnel
        ?.filter(person => person.role === 'SHAREHOLDER')
        ?.map(person => person.name)
        ?.join(', ') || '-- --';

    const personnel =
      each?.key_personnel?.map(person => ({
        name: person.name,
        share_percentage: person.share_percentage || 0
      })) || [];

    const ultimateBeneficiary = personnel.reduce((max, person) => (person.share_percentage > max.share_percentage ? person : max), {
      name: '--',
      share_percentage: 0
    });

    return {
      data: {
        Merchant: (
          <>
            <span className={`status-pill smaller ${switchStatus(each?.merchant.status)}`} />
            <span id="merchant-name">{capitalize(each?.merchant?.name)}</span>
          </>
        ),
        Location: each?.merchant.location,
        Industry: capitalizeRemovedash(each?.merchant.industry || 'N/A'),
        Directors: <span>{capitalize(directors)}</span>,
        'Ultimate Beneficiary (UBO)': (
          <>
            <span data-color="blue"> {(ultimateBeneficiary.share_percentage && ultimateBeneficiary.share_percentage + '%') || '--'}</span>
            <span className=" ml-2">{capitalize(ultimateBeneficiary.name)}</span>
          </>
        ),
        Shareholders: <span>{capitalize(shareholders)}</span>
      }
    };
  }
});
