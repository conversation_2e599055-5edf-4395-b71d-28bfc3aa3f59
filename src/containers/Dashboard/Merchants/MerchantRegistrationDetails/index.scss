@import 'styles/kpy-custom/variables';

.edit-merchant {
  img {
    width: 30px;
    margin-right: 10px;
  }

  @media (min-width: 1000px) {
    width: 20%;
  }
}

.merchant-registrations {
  .payment-preferences-title {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    justify-content: space-between;

    :nth-child(1) {
      order: 2;
    }
  }

  .payment-preferences__table-wrapper {
    margin-top: 3rem;
  }
  .div-table.--merchant-registrations-table {
    &.--heading {
      div {
        width: 100%;
        &:nth-of-type(1) {
          min-width: 25%;
          text-align: left;
        }

        &:nth-of-type(2) {
          min-width: 15%;
          text-align: left;
        }

        &:nth-of-type(3) {
          min-width: 20%;
          text-align: left;
        }

        &:nth-of-type(4) {
          min-width: 20%;
          text-align: left;
        }

        &:nth-of-type(5) {
          min-width: 15%;
          text-align: left;
        }

        &:nth-of-type(6) {
          min-width: 15%;
          text-align: left;
        }
      }
      @media (min-width: 1140px) {
        display: flex;
        font-size: 0.71rem;
        padding: 0.5rem 0;
      }
    }

    &.--heading,
    &.--row {
      @media (min-width: 1140px) {
        div {
          width: 100%;
          &:nth-of-type(1) {
            min-width: 20%;
          }

          &:nth-of-type(2) {
            min-width: 10%;
          }

          &:nth-of-type(3) {
            min-width: 15%;
          }

          &:nth-of-type(4) {
            min-width: 15%;
          }

          &:nth-of-type(5) {
            min-width: 20%;
          }

          &:nth-of-type(6) {
            min-width: 25%;
          }
        }
      }
    }

    &.--row {
      background-color: #ffffff;

      &:hover {
        box-shadow: 0 2px 5px rgba(69, 101, 173, 0.1);
        transform: none;
        background-color: rgba(179, 192, 223, 0.1);
      }

      @media (min-width: 1140px) {
        flex-direction: row;
        font-size: 0.9rem;

        div {
          margin: 0;
        }
      }
    }
  }
  .modal-body {
    max-height: 520px;
    overflow: auto;
    scrollbar-width: none;
  }
  .registration-details-modal {
    .registration-info {
      margin-bottom: 20px;
      .info-heading {
        font-size: 1rem;
        color: #292b2c;
      }

      .info-item {
        border-top: 1px solid #dde2ec;
        padding-top: 1.5rem;
        margin-top: 1.5rem;
        div {
          display: flex;
          p {
            color: #414f5f;
            flex-basis: 40%;
            span {
              color: #94a7b7;
            }

            svg {
              height: 20px;
              width: 20px;
            }
          }
          p:nth-child(2) {
            flex-basis: 60%;
            text-align: start;
          }
        }
      }
    }

    .representative-dropdown {
      background-color: #f9fbfd;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 10px;
      cursor: pointer;

      .dropdown-div {
        display: flex;
        justify-content: space-between;
        width: 100%;
        h4 {
          font-size: 14px;
          color: #636c72;
          flex-basis: 45%;
          font-weight: 400;
        }

        p {
          color: #94a7b7;
          margin-bottom: 0;
          flex-basis: 40%;
        }

        p:nth-child(3) {
          text-align: end;
          span {
            color: #2376f3;
            margin-left: 10px;
            i {
              margin-left: 3px;
              margin-top: 10px;
            }
          }
        }
      }

      .dropdown-content {
        margin-top: 10px;
        border-top: 1px solid #dde2ec;
        padding-top: 15px;
        div {
          display: flex;
          margin-bottom: 20px;

          p {
            color: #94a7b7;
            flex-basis: 40%;
            margin-bottom: 0;
          }
          p:nth-child(2) {
            color: #414f5f;
            text-align: end;
            flex-basis: 60%;
          }
        }
      }
    }
  }
}
.copy-icon {
  img {
    width: 14px;
    height: 14px;
  }
}
