/* eslint-disable react/jsx-props-no-spreading */
import React, { ReactNode } from 'react';
import Flags from 'country-flag-icons/react/3x2';

import Copyable from '+containers/Dashboard/Shared/Copyable';
import Modal from '+containers/Dashboard/Shared/Modal';
import { KeyPersonnelType, MerchantRegistrationType } from '+types';
import { capitalizeRemovedash, getDateAndTime, switchStatus } from '+utils';

import RepresentativesAccordion from './RepresentativesAccordion';

import './index.scss';

type RegistrationDetailsModalType = {
  close: () => void;
  data: MerchantRegistrationType | null;
};

const InfoItem = ({ label, value }: { label: string; value: ReactNode | undefined }) => {
  return (
    <div>
      <p>{label}</p>
      <p>
        <span>{value}</span>
      </p>
    </div>
  );
};

const RegistrationDetailsModal = ({ close, data }: RegistrationDetailsModalType) => {
  const merchantData = data?.merchant;
  const Flag = Flags[merchantData?.iso2] || (() => null);

  const modalContent = (
    <div className="registration-details-modal">
      <div className="registration-info">
        <h2 className="info-heading">Merchant Details</h2>
        <div className="info-item">
          <InfoItem label="Merchant Name" value={merchantData?.name} />
          <InfoItem
            label="Status"
            value={
              <>
                <span className={`status-pill smaller ${switchStatus(merchantData?.status || '')}`} />
                <span>{capitalizeRemovedash(merchantData?.status || 'N/A')}</span>{' '}
              </>
            }
          />
          <InfoItem
            label="Location"
            value={
              <>
                <span>
                  <Flag />
                </span>
                <span>{merchantData?.location}</span>
              </>
            }
          />
          <InfoItem label="Contact Email" value={<Copyable text={merchantData?.email || 'N/A'} buttonClassName="copy-icon" />} />

          <InfoItem label="Industry" value={capitalizeRemovedash(merchantData?.industry || 'N/A')} />
        </div>
      </div>

      <div className="registration-info">
        <h2 className="info-heading">More Details</h2>
        <div className="info-item">
          <InfoItem label="Merchant ID" value={merchantData?.id} />
          <InfoItem label="Date Joined" value={getDateAndTime(data?.created_at)} />
          <InfoItem label="Tier Level" value={merchantData?.tier_level || 'N/A'} />
          <InfoItem label="Representatives" value={merchantData?.number_of_representatives} />
        </div>
      </div>

      {(data?.key_personnel ?? []).length > 0 && (
        <div className="registration-info">
          <h2 className="info-heading">Business Representatives</h2>
          <legend>
            <span />
          </legend>
          <div>
            {data?.key_personnel.map((each: KeyPersonnelType, i) => {
              return <RepresentativesAccordion details={each} key={i} />;
            })}
          </div>
        </div>
      )}
    </div>
  );

  return (
    <Modal
      close={close}
      heading="Merchant Registration Details"
      size="md"
      hideSecondButton
      firstButtonText="Close"
      description="Below, you will find detailed information about the merchant, including business registration and other relevant details."
      content={modalContent}
    />
  );
};

export default RegistrationDetailsModal;
