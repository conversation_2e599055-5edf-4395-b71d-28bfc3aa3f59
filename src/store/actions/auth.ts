import { authConstants } from '../constants';
import APIRequest from '+services/api-services';

const authBaseURL = process.env.REACT_APP_MIDDLEWARE_API_BASE;
const authAPIRequest = new APIRequest(authBaseURL);

export function authorize() {
  function request() {
    return { type: authConstants.OAUTH_AUTHORIZE_REQUEST, noLoader: true };
  }
  function success(user) {
    return { type: authConstants.OAUTH_AUTHORIZE_SUCCESS, user };
  }
  function failure(errors) {
    return { type: authConstants.OAUTH_AUTHORIZE_FAILURE, errors };
  }
  return async dispatch => {
    try {
      await dispatch(request());
      const userDetails = await authAPIRequest.authorizeOauth();
      dispatch(success(userDetails));
    } catch (error) {
      dispatch(failure(error));
    }
  };
}

export function complete(code) {
  function request() {
    return { type: authConstants.OAUTH_COMPLETE_REQUEST, noLoader: true };
  }
  function success(user) {
    return { type: authConstants.OAUTH_COMPLETE_SUCCESS, user };
  }
  function failure(errors) {
    return { type: authConstants.OAUTH_COMPLETE_FAILURE, errors };
  }
  return async dispatch => {
    try {
      const { apiRequest } = await dispatch(request());

      const userDetails = await authAPIRequest.completeOauth(code, apiRequest);
      dispatch(success(userDetails));
    } catch (error) {
      dispatch(failure(error));
    }
  };
}

export function logout() {
  function success() {
    return { type: authConstants.LOGOUT };
  }
  authAPIRequest.logout();
  return async dispatch => {
    dispatch(success());
  };
}

export async function sendMerchantMail(data) {
  const response = await authAPIRequest.merchantEmail(data);
  return response;
}
