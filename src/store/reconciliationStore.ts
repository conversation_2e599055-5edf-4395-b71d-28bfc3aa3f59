import { create } from 'zustand';
import { combine, createJSONStorage, devtools, persist } from 'zustand/middleware';

import { IProcessorReportMapping, KeyMappingType, MappingType, ReconciliationDataType } from '+types';
import { StoreStorage } from '+utils';



interface IStartReconciliationState {
  startReconciliationData: ReconciliationDataType;
  primaryKeyMappings: KeyMappingType;
  comparisonKeyMappings: KeyMappingType;
  referenceKeyMappings: KeyMappingType;
  statusKeyMappings: KeyMappingType;
  autoMatchColumns: boolean;
  bulkDeleteIds: {
    primaryKeyMappings: string[];
    comparisonKeyMappings: string[];
    referenceKeyMappings: string[];
    statusKeyMappings: string[];
  };
}

interface IStartReconciliationAction {
  setStartReconciliationData: (data: Omit<ReconciliationDataType, 'field_mapping'>) => void;
  clearStartReconciliationData: () => void;
  setKeyMappings: (data: KeyMappingType, type: MappingType) => void;
  updateKeyMappings: (data: { id: string; value: Partial<IProcessorReportMapping> }, type: MappingType) => void;
  deleteKeyMapping: (id: string, type: MappingType) => void;
  clearKeyMappings: (type: MappingType | 'all', clearBulkDeleteIds?: boolean) => void;
  setAutoMatchColumns: () => void;
  setBulkDeleteIds: (ids: string[], mappingType: MappingType) => void;
}
const initialState = {
  startReconciliationData: {} as ReconciliationDataType,
  primaryKeyMappings: [] as KeyMappingType,
  comparisonKeyMappings: [] as KeyMappingType,
  referenceKeyMappings: [] as KeyMappingType,
  statusKeyMappings: [] as KeyMappingType,
  autoMatchColumns: false,
  bulkDeleteIds: {
    primaryKeyMappings: [],
    comparisonKeyMappings: [],
    referenceKeyMappings: [],
    statusKeyMappings: []
  }
};

const useReconciliationStore = create(
  devtools(
    persist(
      combine<IStartReconciliationState, IStartReconciliationAction>(initialState, set => ({
        startReconciliationData: initialState.startReconciliationData,
        setBulkDeleteIds: (ids: string[], mappingType: MappingType) => {
          set(state => ({
            ...state,
            bulkDeleteIds: {
              ...state.bulkDeleteIds,
              [mappingType]: ids
            }
          }));
        },
        setStartReconciliationData: (data: Omit<ReconciliationDataType, 'field_mapping'>) => {
          set(state => ({
            ...state,
            startReconciliationData: {
              ...state.startReconciliationData,
              ...data
            }
          }));
        },
        clearStartReconciliationData: () => {
          set(state => ({
            ...state,
            startReconciliationData: {} as ReconciliationDataType
          }));
        },
        setKeyMappings: (data: KeyMappingType, type: MappingType) => {
          set(state => {
            const existingKeys = new Set(state[type].map(mapping => `${mapping.processor_report}:${mapping.internal_report}`));

            const uniqueData = data.filter(mapping => !existingKeys.has(`${mapping.processor_report}:${mapping.internal_report}`));
            return {
              ...state,
              [type]: [...state[type], ...uniqueData]
            };
          });
        },
        updateKeyMappings: (data: { id: string; value: Partial<IProcessorReportMapping> }, type) => {
          set(state => ({
            ...state,
            [type]: state[type].map(mapping => {
              const updatedMapping = data.id === mapping.id ? { ...mapping, ...data.value } : mapping;
              return updatedMapping;
            })
          }));
        },
        deleteKeyMapping: (id: string, type: MappingType) => {
          set(state => ({
            ...state,
            [type]: state[type].filter(mapping => mapping.id !== id)
          }));
        },
        clearKeyMappings: (type: MappingType | 'all', clearBulkDeleteIds = true) =>
          set(state => {
            if (type === 'all') {
              const baseState = {
                ...state,
                primaryKeyMappings: [],
                comparisonKeyMappings: [],
                referenceKeyMappings: [],
                statusKeyMappings: [],
                startReconciliationData: { ...initialState.startReconciliationData }
              };

              return clearBulkDeleteIds ? { ...baseState, bulkDeleteIds: { ...initialState.bulkDeleteIds } } : baseState;
            } else {
              const baseState = { ...state, [type]: [] };

              return clearBulkDeleteIds
                ? {
                    ...baseState,
                    bulkDeleteIds: {
                      ...state.bulkDeleteIds,
                      [type]: []
                    }
                  }
                : baseState;
            }
          }),
        setAutoMatchColumns: () =>
          set(state => ({
            ...state,
            autoMatchColumns: !state.autoMatchColumns
          }))
      })),
      {
        name: 'reconciliation',
        storage: createJSONStorage(() => StoreStorage('local'))
      }
    )
  )
);

export default useReconciliationStore;
